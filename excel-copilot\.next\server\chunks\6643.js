"use strict";exports.id=6643,exports.ids=[6643],exports.modules={38443:(e,r,t)=>{t.d(r,{C:()=>s});var n=t(10326),o=t(79360);t(17577);var i=t(51223);let a=(0,o.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600"}},defaultVariants:{variant:"default"}});function s({className:e,variant:r,...t}){return n.jsx("div",{className:(0,i.cn)(a({variant:r}),e),...t})}},29752:(e,r,t)=>{t.d(r,{Ol:()=>l,SZ:()=>c,Zb:()=>d,aY:()=>m,eW:()=>E,ll:()=>R});var n=t(10326),o=t(31722),i=t(17577),a=t(45365),s=t(51223);let d=(0,i.forwardRef)(({className:e,children:r,hoverable:t=!1,variant:i="default",noPadding:d=!1,animated:l=!1,...R},c)=>{let m=(0,s.cn)("rounded-xl border shadow-sm",{"p-6":!d,"hover:shadow-md hover:-translate-y-1 transition-all duration-200":t&&!l,"border-border bg-card":"default"===i,"border-border/50 bg-transparent":"outline"===i,"bg-card/90 backdrop-blur-md border-border/50":"glass"===i,"bg-gradient-primary text-primary-foreground border-none":"gradient"===i},e);return l?n.jsx(o.E.div,{ref:c,className:m,...(0,a.Ph)("card"),whileHover:t?a.q.hover:void 0,whileTap:t?a.q.tap:void 0,...R,children:r}):n.jsx("div",{ref:c,className:m,...R,children:r})});d.displayName="Card";let l=(0,i.forwardRef)(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("mb-4 flex flex-col space-y-1.5",e),...r}));l.displayName="CardHeader";let R=(0,i.forwardRef)(({className:e,...r},t)=>n.jsx("h3",{ref:t,className:(0,s.cn)("text-xl font-semibold leading-none tracking-tight",e),...r}));R.displayName="CardTitle";let c=(0,i.forwardRef)(({className:e,...r},t)=>n.jsx("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",e),...r}));c.displayName="CardDescription";let m=(0,i.forwardRef)(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("card-content",e),...r}));m.displayName="CardContent";let E=(0,i.forwardRef)(({className:e,...r},t)=>n.jsx("div",{ref:t,className:(0,s.cn)("flex items-center pt-4 mt-auto",e),...r}));E.displayName="CardFooter"},62734:(e,r,t)=>{t.d(r,{RM:()=>d,aF:()=>l});var n=t(17577),o=t.n(n),i=t(51223);let a={default:"border-input",outline:"border-border bg-transparent",ghost:"border-transparent bg-transparent",error:"border-destructive focus-visible:ring-destructive"},s={sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"};function d(e="default",r="md",t=!1,n){return(0,i.cn)("flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a[e],s[r],t&&"min-h-[80px] resize-vertical",n)}function l(e,r){return r?o().createElement("div",{className:r},e):e}},41190:(e,r,t)=>{t.d(r,{I:()=>a,Z:()=>s});var n=t(10326),o=t(17577),i=t(62734);let a=o.forwardRef(({className:e,type:r,wrapperClassName:t,variant:o="default",fieldSize:a="md",inputSize:s,...d},l)=>{let R=n.jsx("input",{type:r,className:(0,i.RM)(o,s||a,!1,e),ref:l,...d});return(0,i.aF)(R,t)});a.displayName="Input";let s=a},50258:(e,r,t)=>{t.d(r,{Kk:()=>E,MD:()=>m});var n=t(10326),o=t(17577),i=t.n(o);class a{constructor(){this.metrics=new Map,this.renderEvents=[],this.isEnabled=!1,this.isEnabled=!1}recordRender(e,r,t=!1,n){if(!this.isEnabled)return;let o=Date.now();this.renderEvents.push({componentName:e,timestamp:o,renderTime:r,props:n||{},isOptimized:t});let i=this.metrics.get(e);i?(i.renderCount++,i.lastRenderTime=r,i.totalRenderTime+=r,i.averageRenderTime=i.totalRenderTime/i.renderCount):this.metrics.set(e,{componentName:e,renderCount:1,lastRenderTime:r,averageRenderTime:r,totalRenderTime:r,isOptimized:t}),this.renderEvents.length>1e3&&(this.renderEvents=this.renderEvents.slice(-1e3))}getComponentMetrics(e){return this.metrics.get(e)}getAllMetrics(){return Array.from(this.metrics.values())}getRecentRenderEvents(e=100){return this.renderEvents.slice(-e)}getPerformanceComparison(){let e=this.getAllMetrics().filter(e=>e.isOptimized),r=this.getAllMetrics().filter(e=>!e.isOptimized),t=e.reduce((e,r)=>e+r.averageRenderTime,0)/e.length||0,n=r.reduce((e,r)=>e+r.averageRenderTime,0)/r.length||0,o=e.reduce((e,r)=>e+r.renderCount,0)/e.length||0,i=r.reduce((e,r)=>e+r.renderCount,0)/r.length||0;return{optimized:e,nonOptimized:r,improvement:{averageRenderTimeReduction:n>0?(n-t)/n*100:0,renderCountReduction:i>0?(i-o)/i*100:0}}}generateReport(){let e=this.getPerformanceComparison(),r=this.metrics.size,t=e.optimized.length,n=e.nonOptimized.length;return`
📊 RELAT\xd3RIO DE PERFORMANCE - COMPONENTES UI

📈 Resumo Geral:
- Total de componentes monitorados: ${r}
- Componentes otimizados: ${t}
- Componentes n\xe3o otimizados: ${n}

🚀 Melhorias de Performance:
- Redu\xe7\xe3o m\xe9dia no tempo de renderiza\xe7\xe3o: ${e.improvement.averageRenderTimeReduction.toFixed(2)}%
- Redu\xe7\xe3o m\xe9dia no n\xfamero de renderiza\xe7\xf5es: ${e.improvement.renderCountReduction.toFixed(2)}%

🔝 Top 5 Componentes Mais Renderizados:
${this.getAllMetrics().sort((e,r)=>r.renderCount-e.renderCount).slice(0,5).map((e,r)=>`${r+1}. ${e.componentName}: ${e.renderCount} renders (${e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado"})`).join("\n")}

⚡ Top 5 Componentes Mais Lentos:
${this.getAllMetrics().sort((e,r)=>r.averageRenderTime-e.averageRenderTime).slice(0,5).map((e,r)=>`${r+1}. ${e.componentName}: ${e.averageRenderTime.toFixed(2)}ms (${e.isOptimized?"✅ Otimizado":"❌ N\xe3o otimizado"})`).join("\n")}
    `.trim()}clear(){this.metrics.clear(),this.renderEvents=[]}exportData(){let e=this.getPerformanceComparison();return{metrics:this.getAllMetrics(),events:this.renderEvents,summary:e}}}let s=new a;function d(e,r=!1){return i().useRef(0),{recordCustomMetric:(t,n)=>{s.recordRender(`${e}.${t}`,n,r)}}}var l=t(91664);function R(e,r){for(let t of["variant","size","disabled","children","className","animated","icon","iconPosition","asChild"])if(e[t]!==r[t])return!1;if("object"==typeof e.children&&"object"==typeof r.children)return e.children===r.children;for(let t of["onClick","onMouseEnter","onMouseLeave","onFocus","onBlur"]){let n=e[t],o=r[t];if((n||o)&&(!n||!o||n!==o))return!1}return!0}let c=(0,o.memo)(l.Button,R),m=i().forwardRef((e,r)=>(d("OptimizedButton",!0),n.jsx(c,{...e,ref:r})));m.displayName="OptimizedButton";let E=(0,o.memo)(({onAction:e,actionId:r,...t})=>{d("ActionButton",!0);let o=i().useCallback(()=>{e()},[e]);return n.jsx(l.Button,{...t,onClick:o})},(e,r)=>R(e,r)&&e.actionId===r.actionId&&e.onAction===r.onAction);E.displayName="ActionButton"},3236:(e,r,t)=>{t.d(r,{x:()=>s});var n=t(10326),o=t(16999),i=t(17577),a=t(51223);let s=i.forwardRef(({className:e,children:r,...t},i)=>(0,n.jsxs)(o.fC,{ref:i,className:(0,a.cn)("relative overflow-hidden",e),...t,children:[n.jsx(o.l_,{className:"h-full w-full rounded-[inherit]",children:r}),n.jsx(d,{}),n.jsx(o.Ns,{})]}));s.displayName=o.fC.displayName;let d=i.forwardRef(({className:e,orientation:r="vertical",...t},i)=>n.jsx(o.gb,{ref:i,orientation:r,className:(0,a.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 border-t border-t-transparent p-[1px]",e),...t,children:n.jsx(o.q4,{className:"relative flex-1 rounded-full bg-border"})}));d.displayName=o.gb.displayName},82631:(e,r,t)=>{t.d(r,{_v:()=>R,aJ:()=>l,pn:()=>s,u:()=>d});var n=t(10326),o=t(39313),i=t(17577),a=t(51223);let s=o.zt,d=o.fC,l=o.xz,R=i.forwardRef(({className:e,sideOffset:r=4,...t},i)=>n.jsx(o.VY,{ref:i,sideOffset:r,className:(0,a.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t}));R.displayName=o.VY.displayName},35342:(e,r,t)=>{var n,o,i,a,s;t.d(r,{ox:()=>n}),function(e){e.FORMULA="FORMULA",e.FILTER="FILTER",e.SORT="SORT",e.FORMAT="FORMAT",e.CHART="CHART",e.CELL_UPDATE="CELL_UPDATE",e.COLUMN_OPERATION="COLUMN_OPERATION",e.ROW_OPERATION="ROW_OPERATION",e.TABLE="TABLE",e.DATA_TRANSFORMATION="DATA_TRANSFORMATION",e.PIVOT_TABLE="PIVOT_TABLE",e.CONDITIONAL_FORMAT="CONDITIONAL_FORMAT",e.ADVANCED_CHART="ADVANCED_CHART",e.ADVANCED_VISUALIZATION="ADVANCED_VISUALIZATION",e.RANGE_UPDATE="RANGE_UPDATE",e.CELL_MERGE="CELL_MERGE",e.CELL_SPLIT="CELL_SPLIT",e.NAMED_RANGE="NAMED_RANGE",e.VALIDATION="VALIDATION",e.FREEZE_PANES="FREEZE_PANES",e.SHEET_OPERATION="SHEET_OPERATION",e.ANALYSIS="ANALYSIS",e.GENERIC="GENERIC"}(n||(n={})),function(e){e.LINE="LINE",e.BAR="BAR",e.COLUMN="COLUMN",e.AREA="AREA",e.SCATTER="SCATTER",e.PIE="PIE"}(o||(o={})),function(e){e.EQUALS="equals",e.NOT_EQUALS="notEquals",e.GREATER_THAN="greaterThan",e.LESS_THAN="lessThan",e.GREATER_THAN_OR_EQUAL="greaterThanOrEqual",e.LESS_THAN_OR_EQUAL="lessThanOrEqual",e.CONTAINS="contains",e.NOT_CONTAINS="notContains",e.BEGINS_WITH="beginsWith",e.ENDS_WITH="endsWith",e.BETWEEN="between"}(i||(i={})),function(e){e.DISCONNECTED="disconnected",e.CONNECTING="connecting",e.CONNECTED="connected",e.ERROR="error"}(a||(a={})),function(e){e.FORMULA_ERROR="FORMULA_ERROR",e.REFERENCE_ERROR="REFERENCE_ERROR",e.VALUE_ERROR="VALUE_ERROR",e.NAME_ERROR="NAME_ERROR",e.RANGE_ERROR="RANGE_ERROR",e.SYNTAX_ERROR="SYNTAX_ERROR",e.DATA_VALIDATION_ERROR="DATA_VALIDATION_ERROR",e.FORMAT_ERROR="FORMAT_ERROR",e.OPERATION_NOT_SUPPORTED="OPERATION_NOT_SUPPORTED",e.UNKNOWN_ERROR="UNKNOWN_ERROR"}(s||(s={}))}};