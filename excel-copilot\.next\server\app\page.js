(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},94007:e=>{"use strict";e.exports=require("@prisma/client")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},98188:e=>{"use strict";e.exports=require("module")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},71267:e=>{"use strict";e.exports=require("worker_threads")},63021:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),a(44880),a(65675),a(12523);var r=a(23191),s=a(88716),n=a(37922),o=a.n(n),i=a(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,44880)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,65675)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,12523)),"C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\not-found.tsx"]}],d=["C:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\app\\page.tsx"],u="/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81710:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,79404,23)),Promise.resolve().then(a.bind(a,55685)),Promise.resolve().then(a.bind(a,33294)),Promise.resolve().then(a.bind(a,91664))},37202:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},941:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},75290:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});let r=(0,a(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55685:(e,t,a)=>{"use strict";a.d(t,{CommandExamplesWrapper:()=>A});var r=a(10326),s=a(35047),n=a(72257),o=a(95396),i=a(76557);let l=(0,i.Z)("FilterX",[["path",{d:"M13.013 3H2l8 9.46V19l4 2v-8.54l.9-1.055",key:"1fi1da"}],["path",{d:"m22 3-5 5",key:"12jva0"}],["path",{d:"m17 3 5 5",key:"k36vhe"}]]);var c=a(4198);let d=(0,i.Z)("Sigma",[["path",{d:"M18 7V4H6l6 8-6 8h12v-3",key:"zis8ev"}]]);var u=a(1572),m=a(35351),p=a(941),x=a(17577),h=a(38443),f=a(91664),g=a(2578),b=a(41190),y=a(50258),v=a(3236),j=a(82015),k=(a(75290),a(72607),a(60850),a(14831),a(82631),a(51223),a(56627));k.s6,h.C,f.Button,g.ZP,b.Z,y.MD,y.Kk,v.x,j.Z,k.V6;var w=a(29752);function N({onSelect:e}){let[t,a]=(0,x.useState)(!1),[s,i]=(0,x.useState)(-1),[h,g]=(0,x.useState)(!1),b=[{name:"An\xe1lise Matem\xe1tica",icon:r.jsx(n.Z,{className:"h-5 w-5"}),examples:["Some os valores da coluna B","Calcule a m\xe9dia da coluna Vendas","Qual \xe9 o maior valor na coluna de Receita?","Encontre o desvio padr\xe3o dos dados na coluna E","Multiplique os valores da coluna A por 2","Calcule o percentual de crescimento m\xeas a m\xeas","Fa\xe7a uma an\xe1lise estat\xedstica completa da coluna Valores"]},{name:"Visualiza\xe7\xe3o",icon:r.jsx(o.Z,{className:"h-5 w-5"}),examples:["Crie um gr\xe1fico de barras com os dados das colunas A e B","Fa\xe7a um gr\xe1fico de linha de vendas por m\xeas","Gere um gr\xe1fico de pizza com os valores da tabela","Mostre um gr\xe1fico de dispers\xe3o entre pre\xe7o e quantidade","Crie um histograma da coluna Idades","Fa\xe7a um dashboard com 3 gr\xe1ficos: barras, linha e pizza","Crie um mapa de calor com os dados de vendas por regi\xe3o"]},{name:"Filtros e Organiza\xe7\xe3o",icon:r.jsx(l,{className:"h-5 w-5"}),examples:["Filtre os dados onde Vendas > 1000","Ordene a tabela por valor, do maior para o menor","Mostre apenas registros onde a coluna Regi\xe3o \xe9 'Sul'","Filtre dados do m\xeas de Janeiro","Remova as linhas com valores nulos","Filtre os 10 maiores clientes por volume de compras","Agrupe por categoria e mostre apenas os grupos com mais de 5 itens"]},{name:"Formata\xe7\xe3o",icon:r.jsx(c.Z,{className:"h-5 w-5"}),examples:["Converta a planilha atual para formato de tabela","Aplique formata\xe7\xe3o condicional na coluna de valores","Destaque c\xe9lulas com valores negativos em vermelho","Formate a coluna de datas no padr\xe3o DD/MM/AAAA","Adicione uma linha de totais ao final da tabela","Formate os cabe\xe7alhos com fundo azul e texto branco em negrito","Adicione bordas em todas as c\xe9lulas da tabela"]},{name:"Tabelas Din\xe2micas",icon:r.jsx(d,{className:"h-5 w-5"}),examples:["Crie uma tabela din\xe2mica agrupando vendas por regi\xe3o","Fa\xe7a uma tabela din\xe2mica com vendas por produto e vendedor","Gere uma tabela din\xe2mica de receitas mensais por categoria","Crie um resumo de vendas por trimestre e regi\xe3o","Fa\xe7a uma tabela din\xe2mica com subtotais por departamento"]},{name:"An\xe1lise Avan\xe7ada",icon:r.jsx(u.Z,{className:"h-5 w-5"}),examples:["Fa\xe7a uma an\xe1lise de correla\xe7\xe3o entre as colunas pre\xe7o e demanda","Gere estat\xedsticas descritivas de todas as colunas num\xe9ricas","Crie segmenta\xe7\xe3o de dados por faixa et\xe1ria: 0-18, 19-35, 36-60, 60+","Aplique regress\xe3o linear e preveja valores futuros","Fa\xe7a uma an\xe1lise de sazonalidade nos dados mensais","Identifique outliers na coluna de vendas e sugira tratamentos","Calcule a previs\xe3o de vendas para os pr\xf3ximos 3 meses usando s\xe9rie temporal","Agrupe clientes por comportamento de compra usando K-means","Fa\xe7a uma an\xe1lise de cesta de compras para identificar produtos complementares","Crie um modelo de pontua\xe7\xe3o para classificar leads por potencial"]},{name:"Perguntas em Linguagem Natural",icon:r.jsx(m.Z,{className:"h-5 w-5"}),examples:["Quais foram os 3 melhores vendedores no \xfaltimo trimestre?","Qual regi\xe3o teve a maior queda nas vendas?","Como est\xe1 o desempenho das vendas comparado com o mesmo per\xedodo do ano passado?","Quais produtos tiveram crescimento acima de 10% nos \xfaltimos 6 meses?","Qual \xe9 a tend\xeancia de vendas para o produto X?"]}],j=b[0]||{name:"An\xe1lise Matem\xe1tica",icon:r.jsx(n.Z,{className:"h-5 w-5"}),examples:["Some os valores da coluna B"]},k=j.examples[0]||"",N=(0,x.useCallback)((t,a,r)=>{let s=t.key;if("Enter"===s||" "===s){t.preventDefault();let s=b[a];s&&s.examples[r]&&e(s.examples[r])}if("ArrowDown"===s||"ArrowUp"===s){t.preventDefault();let e=b[a];if(!e)return;let n=e.examples.length-1;"ArrowDown"===s&&r<n?(i(r+1),document.getElementById(`example-${a}-${r+1}`)?.focus()):"ArrowUp"===s&&r>0&&(i(r-1),document.getElementById(`example-${a}-${r-1}`)?.focus())}},[e]);return r.jsx("div",{className:"space-y-4 w-full",children:(0,r.jsxs)(w.Zb,{className:"border shadow-sm bg-card",children:[r.jsx(w.Ol,{className:"pb-2",children:(0,r.jsxs)(w.ll,{className:"text-lg flex items-center",children:[r.jsx(u.Z,{className:"h-6 w-6 mr-2 text-primary","aria-hidden":"true"}),r.jsx("span",{className:"text-enhanced-contrast",children:"Exemplos de Comandos"})]})}),(0,r.jsxs)(w.aY,{children:[(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[r.jsx("div",{className:"p-1 rounded-md bg-primary/10 text-primary","aria-hidden":"true",children:j.icon}),r.jsx("span",{className:"text-xs font-medium text-muted-foreground",children:j.name})]}),r.jsx(f.Button,{variant:"outline",className:"w-full justify-start text-left font-normal h-auto py-3 border-primary/20 bg-primary/5 hover:bg-primary/10 a11y-focus",onClick:()=>e(k),"aria-label":`Usar comando: ${k}`,children:r.jsx("span",{className:"line-clamp-2 text-xs sm:text-sm",children:k})})]}),t?(0,r.jsxs)(r.Fragment,{children:[r.jsx(v.x,{className:"h-[200px] sm:h-[250px] md:h-[300px] pr-4",role:"list","aria-label":"Lista de comandos por categoria",children:r.jsx("div",{className:"space-y-3 sm:space-y-4",children:b.map((t,a)=>(0,r.jsxs)("div",{className:"space-y-1.5 sm:space-y-2",role:"group","aria-labelledby":`category-${a}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("div",{className:"p-1 sm:p-1.5 rounded-md bg-muted text-muted-foreground","aria-hidden":"true",children:t.icon}),r.jsx("span",{className:"text-xs sm:text-sm font-medium",id:`category-${a}`,children:t.name})]}),r.jsx("div",{className:"grid grid-cols-1 gap-1 sm:gap-1.5 pl-5 sm:pl-7",role:"list",children:t.examples.slice(0,h?4:void 0).map((t,s)=>r.jsx(y.Kk,{id:`example-${a}-${s}`,variant:"ghost",size:"sm",className:"justify-start h-auto py-1 sm:py-1.5 px-1.5 sm:px-2 text-xs sm:text-sm font-normal text-muted-foreground hover:text-foreground mobile-enhanced-tap a11y-focus",actionId:`${a}-${s}`,onAction:()=>e(t),onKeyDown:e=>N(e,a,s),"aria-label":`Usar comando: ${t}`,children:r.jsx("span",{className:"line-clamp-1",children:t})},s))})]},a))})}),(0,r.jsxs)(f.Button,{onClick:()=>a(!1),variant:"ghost",size:"sm",className:"w-full mt-2 text-sm text-muted-foreground hover:text-foreground flex items-center justify-center","aria-expanded":"true","aria-label":"Mostrar menos exemplos",children:[r.jsx("span",{children:"Mostrar menos"}),r.jsx(p.Z,{className:"h-4 w-4 ml-1 transform rotate-180"})]})]}):(0,r.jsxs)(f.Button,{onClick:()=>a(!0),variant:"ghost",size:"sm",className:"w-full text-sm text-muted-foreground hover:text-foreground flex items-center justify-center","aria-expanded":"false","aria-label":"Mostrar mais exemplos",children:[r.jsx("span",{children:"Explorar mais sugest\xf5es"}),r.jsx(p.Z,{className:"h-4 w-4 ml-1"})]})]})]})})}function A(){let e=(0,s.useRouter)();return r.jsx(N,{onSelect:t=>{e.push(`/dashboard?command=${encodeURIComponent(t)}`)}})}},33294:(e,t,a)=>{"use strict";a.r(t),a.d(t,{HeroSection:()=>ev});var r=a(10326),s=a(17577),n=a.n(s),o=a(21058),i=a(34374),l=a(31722),c=a(1572),d=a(24230),u=a(32933),m=a(90434);function p(e,t,a,r){return new(a||(a=Promise))(function(s,n){function o(e){try{l(r.next(e))}catch(e){n(e)}}function i(e){try{l(r.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof a?t:new a(function(e){e(t)})).then(o,i)}l((r=r.apply(e,t||[])).next())})}function x(e,t){var a,r,s,n,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]};return n={next:i(0),throw:i(1),return:i(2)},"function"==typeof Symbol&&(n[Symbol.iterator]=function(){return this}),n;function i(n){return function(i){return function(n){if(a)throw TypeError("Generator is already executing.");for(;o;)try{if(a=1,r&&(s=2&n[0]?r.return:n[0]?r.throw||((s=r.return)&&s.call(r),0):r.next)&&!(s=s.call(r,n[1])).done)return s;switch(r=0,s&&(n=[2&n[0],s.value]),n[0]){case 0:case 1:s=n;break;case 4:return o.label++,{value:n[1],done:!1};case 5:o.label++,r=n[1],n=[0];continue;case 7:n=o.ops.pop(),o.trys.pop();continue;default:if(!(s=(s=o.trys).length>0&&s[s.length-1])&&(6===n[0]||2===n[0])){o=0;continue}if(3===n[0]&&(!s||n[1]>s[0]&&n[1]<s[3])){o.label=n[1];break}if(6===n[0]&&o.label<s[1]){o.label=s[1],s=n;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(n);break}s[2]&&o.ops.pop(),o.trys.pop();continue}n=t.call(e,o)}catch(e){n=[6,e],r=0}finally{a=s=0}if(5&n[0])throw n[1];return{value:n[0]?n[1]:void 0,done:!0}}([n,i])}}}function h(e){var t="function"==typeof Symbol&&Symbol.iterator,a=t&&e[t],r=0;if(a)return a.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function f(e,t){var a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;var r,s,n=a.call(e),o=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)o.push(r.value)}catch(e){s={error:e}}finally{try{r&&!r.done&&(a=n.return)&&a.call(n)}finally{if(s)throw s.error}}return o}function g(e,t,a){if(a||2==arguments.length)for(var r,s=0,n=t.length;s<n;s++)!r&&s in t||(r||(r=Array.prototype.slice.call(t,0,s)),r[s]=t[s]);return e.concat(r||Array.prototype.slice.call(t))}function b(e,t,a,r,s){for(var n=[],o=5;o<arguments.length;o++)n[o-5]=arguments[o];return p(this,void 0,void 0,function(){var o,i,l,c,d;return x(this,function(u){switch(u.label){case 0:u.trys.push([0,12,13,14]),i=(o=h(n)).next(),u.label=1;case 1:if(i.done)return[3,11];switch(typeof(l=i.value)){case"string":return[3,2];case"number":return[3,4];case"function":return[3,6]}return[3,8];case 2:return[4,function(e,t,a,r,s,n){return p(this,void 0,void 0,function(){var o,i;return x(this,function(l){switch(l.label){case 0:var c,d;return c=o=e.textContent||"",d=f(a).slice(0),i=g(g([],f(c),!1),[NaN],!1).findIndex(function(e,t){return d[t]!==e}),[4,function(e,t,a,r,s){return p(this,void 0,void 0,function(){var n,o,i,l,c,d,u,m,p,g,b,v;return x(this,function(j){switch(j.label){case 0:if(n=t,s){for(o=0,i=1;i<t.length;i++)if(c=(l=f([t[i-1],t[i]],2))[0],(d=l[1]).length>c.length||""===d){o=i;break}n=t.slice(o,t.length)}j.label=1;case 1:j.trys.push([1,6,7,8]),m=(u=h(function(e){var t,a,r,s,n,o;return x(this,function(i){switch(i.label){case 0:t=function(e){return x(this,function(t){switch(t.label){case 0:return[4,{op:function(t){return requestAnimationFrame(function(){return t.textContent=e})},opCode:function(t){var a=t.textContent||"";return""===e||a.length>e.length?"DELETE":"WRITING"}}];case 1:return t.sent(),[2]}})},i.label=1;case 1:i.trys.push([1,6,7,8]),r=(a=h(e)).next(),i.label=2;case 2:return r.done?[3,5]:(s=r.value,[5,t(s)]);case 3:i.sent(),i.label=4;case 4:return r=a.next(),[3,2];case 5:return[3,8];case 6:return n={error:i.sent()},[3,8];case 7:try{r&&!r.done&&(o=a.return)&&o.call(a)}finally{if(n)throw n.error}return[7];case 8:return[2]}})}(n))).next(),j.label=2;case 2:return m.done?[3,5]:(g="WRITING"===(p=m.value).opCode(e)?a+a*(Math.random()-.5):r+r*(Math.random()-.5),p.op(e),[4,y(g)]);case 3:j.sent(),j.label=4;case 4:return m=u.next(),[3,2];case 5:return[3,8];case 6:return b={error:j.sent()},[3,8];case 7:try{m&&!m.done&&(v=u.return)&&v.call(u)}finally{if(b)throw b.error}return[7];case 8:return[2]}})})}(e,g(g([],f(function(e,t,a){var r,s;return void 0===a&&(a=0),x(this,function(n){switch(n.label){case 0:s=(r=t(e)).length,n.label=1;case 1:return s>a?[4,r.slice(0,--s).join("")]:[3,3];case 2:return n.sent(),[3,1];case 3:return[2]}})}(o,t,i)),!1),f(function(e,t,a){var r,s;return void 0===a&&(a=0),x(this,function(n){switch(n.label){case 0:s=(r=t(e)).length,n.label=1;case 1:return a<s?[4,r.slice(0,++a).join("")]:[3,3];case 2:return n.sent(),[3,1];case 3:return[2]}})}(a,t,i)),!1),r,s,n)];case 1:return l.sent(),[2]}})})}(e,t,l,a,r,s)];case 3:case 5:case 7:return u.sent(),[3,10];case 4:return[4,y(l)];case 6:return[4,l.apply(void 0,g([e,t,a,r,s],f(n),!1))];case 8:return[4,l];case 9:u.sent(),u.label=10;case 10:return i=o.next(),[3,1];case 11:return[3,14];case 12:return c={error:u.sent()},[3,14];case 13:try{i&&!i.done&&(d=o.return)&&d.call(o)}finally{if(c)throw c.error}return[7];case 14:return[2]}})})}function y(e){return p(this,void 0,void 0,function(){return x(this,function(t){switch(t.label){case 0:return[4,new Promise(function(t){return setTimeout(t,e)})];case 1:return t.sent(),[2]}})})}!function(e,t){void 0===t&&(t={});var a=t.insertAt;if(e&&"undefined"!=typeof document){var r=document.head||document.getElementsByTagName("head")[0],s=document.createElement("style");s.type="text/css","top"===a&&r.firstChild?r.insertBefore(s,r.firstChild):r.appendChild(s),s.styleSheet?s.styleSheet.cssText=e:s.appendChild(document.createTextNode(e))}}(".index-module_type__E-SaG::after {\n  content: '|';\n  animation: index-module_cursor__PQg0P 1.1s infinite step-start;\n}\n\n@keyframes index-module_cursor__PQg0P {\n  50% {\n    opacity: 0;\n  }\n}\n");var v=(0,s.memo)((0,s.forwardRef)(function(e,t){var a=e.sequence,r=e.repeat,o=e.className,i=e.speed,l=void 0===i?40:i,c=e.deletionSpeed,d=e.omitDeletionAnimation,u=void 0!==d&&d,m=e.preRenderFirstString,p=e.wrapper,x=e.splitter,h=void 0===x?function(e){return g([],f(e),!1)}:x,y=e.cursor,v=void 0===y||y,j=e.style,k=function(e,t){var a={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(a[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(r=Object.getOwnPropertySymbols(e);s<r.length;s++)0>t.indexOf(r[s])&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(a[r[s]]=e[r[s]])}return a}(e,["sequence","repeat","className","speed","deletionSpeed","omitDeletionAnimation","preRenderFirstString","wrapper","splitter","cursor","style"]),w=k["aria-label"],N=k["aria-hidden"],A=k.role;c||(c=l);var S=[,,].fill(40);[l,c].forEach(function(e,t){switch(typeof e){case"number":S[t]=Math.abs(e-100);break;case"object":var a=e.type,r=e.value;if("number"!=typeof r)break;"keyStrokeDelayInMs"===a&&(S[t]=r)}});var C,T,E,O,P,D,M,F,_=S[0],I=S[1],z=(void 0===C&&(C=null),T=(0,s.useRef)(C),(0,s.useEffect)(function(){t&&("function"==typeof t?t(T.current):t.current=T.current)},[t]),T),Z="index-module_type__E-SaG";E=o?"".concat(v?Z+" ":"").concat(o):v?Z:"",O=(0,s.useRef)(function(){var e,t=a;r===1/0?e=b:"number"==typeof r&&(t=Array(1+r).fill(a).flat());var s=e?g(g([],f(t),!1),[e],!1):g([],f(t),!1);return b.apply(void 0,g([z.current,h,_,I,u],f(s),!1)),function(){z.current}}),P=(0,s.useRef)(),D=(0,s.useRef)(!1),M=(0,s.useRef)(!1),F=f((0,s.useState)(0),2)[1],D.current&&(M.current=!0),(0,s.useEffect)(function(){return D.current||(P.current=O.current(),D.current=!0),F(function(e){return e+1}),function(){M.current&&P.current&&P.current()}},[]);var R=void 0!==m&&m?a.find(function(e){return"string"==typeof e})||"":null;return n().createElement(void 0===p?"span":p,{"aria-hidden":N,"aria-label":w,role:A,style:j,className:E,children:w?n().createElement("span",{"aria-hidden":"true",ref:z,children:R}):R,ref:w?void 0:z})}),function(e,t){return!0}),j=a(30660),k=a(49844),w=a(18423),N=a(62637),A=a(9997),S=a(44675),C=a(69274),T=a(8564),E=a(96601),O=a(23958),P=a(85586),D=a.n(P),M=a(20119),F=a.n(M),_=a(81711),I=a.n(_),z=a(41135),Z=a(96195),R=a(61595),q=a(49432),L=a(45588),$=a(22582),B=a(2645),V=a(2656),U=a(81656),K=a(81546),G=["type","layout","connectNulls","ref"],W=["key"];function H(e){return(H="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function Q(e,t){if(null==e)return{};var a,r,s=function(e,t){if(null==e)return{};var a={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;a[r]=e[r]}return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)a=n[r],!(t.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(s[a]=e[a])}return s}function X(){return(X=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(this,arguments)}function J(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,r)}return a}function Y(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?J(Object(a),!0).forEach(function(t){eo(e,t,a[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):J(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function ee(e){return function(e){if(Array.isArray(e))return et(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return et(e,void 0);var a=Object.prototype.toString.call(e).slice(8,-1);if("Object"===a&&e.constructor&&(a=e.constructor.name),"Map"===a||"Set"===a)return Array.from(e);if("Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a))return et(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function et(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function ea(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ei(r.key),r)}}function er(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(er=function(){return!!e})()}function es(e){return(es=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function en(e,t){return(en=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eo(e,t,a){return(t=ei(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function ei(e){var t=function(e,t){if("object"!=H(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=H(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==H(t)?t:t+""}var el=function(e){var t,a;function r(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r);for(var e,t,a,s=arguments.length,n=Array(s),o=0;o<s;o++)n[o]=arguments[o];return t=r,a=[].concat(n),t=es(t),eo(e=function(e,t){if(t&&("object"===H(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,er()?Reflect.construct(t,a||[],es(this).constructor):t.apply(this,a)),"state",{isAnimationFinished:!0,totalLength:0}),eo(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),eo(e,"getStrokeDasharray",function(t,a,s){var n=s.reduce(function(e,t){return e+t});if(!n)return e.generateSimpleStrokeDasharray(a,t);for(var o=t%n,i=a-t,l=[],c=0,d=0;c<s.length;d+=s[c],++c)if(d+s[c]>o){l=[].concat(ee(s.slice(0,c)),[o-d]);break}var u=l.length%2==0?[0,i]:[i];return[].concat(ee(r.repeat(s,Math.floor(t/n))),ee(l),u).map(function(e){return"".concat(e,"px")}).join(", ")}),eo(e,"id",(0,B.EL)("recharts-line-")),eo(e,"pathRef",function(t){e.mainCurve=t}),eo(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),eo(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&en(e,t)}(r,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,r=a.points,s=a.xAxis,o=a.yAxis,i=a.layout,l=a.children,c=(0,V.NN)(l,$.W);if(!c)return null;var d=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,K.F$)(e.payload,t)}};return n().createElement(q.m,{clipPath:e?"url(#clipPath-".concat(t,")"):null},c.map(function(e){return n().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:r,xAxis:s,yAxis:o,layout:i,dataPointFormatter:d})}))}},{key:"renderDots",value:function(e,t,a){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var s=this.props,o=s.dot,i=s.points,l=s.dataKey,c=(0,V.L6)(this.props,!1),d=(0,V.L6)(o,!0),u=i.map(function(e,t){var a=Y(Y(Y({key:"dot-".concat(t),r:3},c),d),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:l,payload:e.payload,points:i});return r.renderDotItem(o,a)}),m={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(a,")"):null};return n().createElement(q.m,X({className:"recharts-line-dots",key:"dots"},m),u)}},{key:"renderCurveStatically",value:function(e,t,a,r){var s=this.props,o=s.type,i=s.layout,l=s.connectNulls,c=(s.ref,Q(s,G)),d=Y(Y(Y({},(0,V.L6)(c,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(a,")"):null,points:e},r),{},{type:o,layout:i,connectNulls:l});return n().createElement(Z.H,X({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var a=this,r=this.props,s=r.points,o=r.strokeDasharray,i=r.isAnimationActive,l=r.animationBegin,c=r.animationDuration,d=r.animationEasing,u=r.animationId,m=r.animateNewValues,p=r.width,x=r.height,h=this.state,f=h.prevPoints,g=h.totalLength;return n().createElement(O.ZP,{begin:l,duration:c,isActive:i,easing:d,from:{t:0},to:{t:1},key:"line-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(r){var n,i=r.t;if(f){var l=f.length/s.length,c=s.map(function(e,t){var a=Math.floor(t*l);if(f[a]){var r=f[a],s=(0,B.k4)(r.x,e.x),n=(0,B.k4)(r.y,e.y);return Y(Y({},e),{},{x:s(i),y:n(i)})}if(m){var o=(0,B.k4)(2*p,e.x),c=(0,B.k4)(x/2,e.y);return Y(Y({},e),{},{x:o(i),y:c(i)})}return Y(Y({},e),{},{x:e.x,y:e.y})});return a.renderCurveStatically(c,e,t)}var d=(0,B.k4)(0,g)(i);if(o){var u="".concat(o).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});n=a.getStrokeDasharray(d,g,u)}else n=a.generateSimpleStrokeDasharray(g,d);return a.renderCurveStatically(s,e,t,{strokeDasharray:n})})}},{key:"renderCurve",value:function(e,t){var a=this.props,r=a.points,s=a.isAnimationActive,n=this.state,o=n.prevPoints,i=n.totalLength;return s&&r&&r.length&&(!o&&i>0||!I()(o,r))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(r,e,t)}},{key:"render",value:function(){var e,t=this.props,a=t.hide,r=t.dot,s=t.points,o=t.className,i=t.xAxis,l=t.yAxis,c=t.top,d=t.left,u=t.width,m=t.height,p=t.isAnimationActive,x=t.id;if(a||!s||!s.length)return null;var h=this.state.isAnimationFinished,f=1===s.length,g=(0,z.Z)("recharts-line",o),b=i&&i.allowDataOverflow,y=l&&l.allowDataOverflow,v=b||y,j=F()(x)?this.id:x,k=null!==(e=(0,V.L6)(r,!1))&&void 0!==e?e:{r:3,strokeWidth:2},w=k.r,N=k.strokeWidth,A=((0,V.jf)(r)?r:{}).clipDot,S=void 0===A||A,C=2*(void 0===w?3:w)+(void 0===N?2:N);return n().createElement(q.m,{className:g},b||y?n().createElement("defs",null,n().createElement("clipPath",{id:"clipPath-".concat(j)},n().createElement("rect",{x:b?d:d-u/2,y:y?c:c-m/2,width:b?u:2*u,height:y?m:2*m})),!S&&n().createElement("clipPath",{id:"clipPath-dots-".concat(j)},n().createElement("rect",{x:d-C/2,y:c-C/2,width:u+C,height:m+C}))):null,!f&&this.renderCurve(v,j),this.renderErrorBar(v,j),(f||r)&&this.renderDots(v,S,j),(!p||h)&&L.e.renderCallByParent(this.props,s))}}],a=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var a=e.length%2!=0?[].concat(ee(e),[0]):e,r=[],s=0;s<t;++s)r=[].concat(ee(r),ee(a));return r}},{key:"renderDotItem",value:function(e,t){var a;if(n().isValidElement(e))a=n().cloneElement(e,t);else if(D()(e))a=e(t);else{var r=t.key,s=Q(t,W),o=(0,z.Z)("recharts-line-dot","boolean"!=typeof e?e.className:"");a=n().createElement(R.o,X({key:r},s,{className:o}))}return a}}],t&&ea(r.prototype,t),a&&ea(r,a),Object.defineProperty(r,"prototype",{writable:!1}),r}(s.PureComponent);eo(el,"displayName","Line"),eo(el,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!U.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),eo(el,"getComposedData",function(e){var t=e.props,a=e.xAxis,r=e.yAxis,s=e.xAxisTicks,n=e.yAxisTicks,o=e.dataKey,i=e.bandSize,l=e.displayedData,c=e.offset,d=t.layout;return Y({points:l.map(function(e,t){var l=(0,K.F$)(e,o);return"horizontal"===d?{x:(0,K.Hv)({axis:a,ticks:s,bandSize:i,entry:e,index:t}),y:F()(l)?null:r.scale(l),value:l,payload:e}:{x:F()(l)?null:a.scale(l),y:(0,K.Hv)({axis:r,ticks:n,bandSize:i,entry:e,index:t}),value:l,payload:e}}),layout:d},c)});var ec=a(11327),ed=(0,E.z)({chartName:"LineChart",GraphicalChild:el,axisComponents:[{axisType:"xAxis",AxisComp:N.K},{axisType:"yAxis",AxisComp:A.B}],formatAxisMap:ec.t9}),eu=a(20751),em=a(78948),ep=a(99630),ex=a(38443),eh=a(91664),ef=a(51223),eg=a(35342);let eb=["#0088FE","#00C49F","#FFBB28","#FF8042","#8884D8","#82CA9D","#FFC658","#8DD1E1"],ey=[{type:"message",content:'Posso ajudar voc\xea a trabalhar com suas planilhas usando comandos em linguagem natural. Por exemplo, tente "Crie uma tabela de vendas por regi\xe3o" ou "Calcule a m\xe9dia da coluna B".'},{type:eg.ox.TABLE,command:"Crie uma tabela com vendas por regi\xe3o",content:[["Regi\xe3o","Vendas","Meta","% Atingimento"],["Norte","12500","15000","83%"],["Sul","18200","16000","114%"],["Leste","14800","14000","106%"],["Oeste","9300","12000","78%"],["Centro","11700","10000","117%"]]},{type:eg.ox.CHART,command:"Crie um gr\xe1fico de vendas por regi\xe3o",chartType:"bar",data:{labels:["Norte","Sul","Leste","Oeste","Centro"],datasets:[{label:"Vendas",data:[12500,18200,14800,9300,11700],backgroundColor:"rgba(53, 162, 235, 0.5)"},{label:"Meta",data:[15e3,16e3,14e3,12e3,1e4],backgroundColor:"rgba(255, 99, 132, 0.5)"}]}},{type:eg.ox.TABLE,command:"Fa\xe7a uma an\xe1lise das vendas",content:[["M\xe9trica","Valor","Status"],["Total de Vendas","R$ 66.500,00","↑ 12%"],["M\xe9dia por Regi\xe3o","R$ 13.300,00","-"],["Maior Desempenho","Sul (114%)","★★★"],["Menor Desempenho","Oeste (78%)","⚠️"],["Regi\xf5es Acima da Meta","3","↑ 1"]]}];function ev(){let[e,t]=(0,s.useState)(0),[a,n]=(0,s.useState)(!1),[p,x]=(0,s.useState)(!1),[h,f]=(0,s.useState)(!1),[g,b]=(0,s.useState)([]),[y,E]=(0,s.useState)(!1),O=function(){i.O.current||(0,o.A)();let[e]=(0,s.useState)(i.n.current);return e}(),P=(0,s.useMemo)(()=>O||y?{initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},disableMotion:!0}:{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3},disableMotion:!1},[O,y]),D=(0,s.useCallback)(t=>{n(!0),0===e?b(ey.slice(0,2)):1===e?b(ey.slice(0,4)):2===e&&b(ey)},[e]),M=(0,s.useCallback)(()=>{if(e<ey.length-1){f(!0);let a=e+1;t(a),setTimeout(()=>{f(!1),D(ey[a]?.command||"")},1500)}else t(0),n(!1),b([])},[e,D]);return(0,s.useCallback)(()=>{if(e>0){f(!0);let a=e-1;t(a),setTimeout(()=>{f(!1),D(ey[a]?.command||"")},500)}},[e,D]),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-8 py-8",children:[(0,r.jsxs)("div",{className:"text-left md:w-1/2",children:[(0,r.jsxs)("div",{className:"inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 backdrop-blur-sm px-3 py-1 mb-4 shadow-sm",children:[r.jsx(c.Z,{className:"h-3 w-3 mr-1 text-blue-600 dark:text-blue-400"}),r.jsx("span",{className:"text-xs font-medium text-blue-700 dark:text-blue-300",children:"IA para Excel"})]}),r.jsx("h1",{className:"text-4xl md:text-5xl font-bold tracking-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 leading-tight",children:"Planilhas inteligentes com linguagem natural"}),r.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl",children:"Excel Copilot transforma comandos em texto simples em planilhas poderosas."}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-3",children:[r.jsx(eh.Button,{size:"lg",className:"rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0 px-6 py-2 text-sm shadow-md font-medium transition-all",asChild:!0,children:(0,r.jsxs)(m.default,{href:"/dashboard",children:["Come\xe7ar agora ",r.jsx(d.Z,{className:"ml-1 h-4 w-4"})]})}),r.jsx(eh.Button,{variant:"outline",size:"lg",className:"rounded-full border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700 px-6 py-2 text-sm font-medium transition-all",asChild:!0,children:r.jsx(m.default,{href:"#exemplos",children:"Ver exemplos"})})]}),(0,r.jsxs)("div",{className:"mt-6 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[r.jsx(u.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),r.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Comandos em linguagem natural para criar planilhas"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[r.jsx(u.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),r.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"F\xf3rmulas complexas geradas automaticamente"})]}),(0,r.jsxs)("div",{className:"flex items-start gap-2",children:[r.jsx(u.Z,{className:"h-4 w-4 text-green-600 dark:text-green-400 mt-0.5"}),r.jsx("p",{className:"text-sm text-gray-700 dark:text-gray-300",children:"Visualiza\xe7\xf5es e gr\xe1ficos em segundos"})]})]})]}),r.jsx("div",{className:"md:w-1/2 w-full mt-8 md:mt-0",children:(0,r.jsxs)("div",{className:"rounded-xl border shadow-md bg-white dark:bg-gray-900/60 overflow-hidden backdrop-blur-sm",children:[(0,r.jsxs)("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 border-b flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex space-x-1.5",children:[r.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"}),r.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"}),r.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"})]}),r.jsx(ex.C,{variant:"outline",className:"text-xs font-normal hidden xs:inline",children:"Demo Interativa"}),r.jsx(eh.Button,{id:"autoplay-button",variant:"ghost",size:"sm",onClick:()=>x(!p),className:"text-xs","aria-pressed":p,"aria-label":p?"Desativar execu\xe7\xe3o autom\xe1tica":"Ativar execu\xe7\xe3o autom\xe1tica",children:p?"Pausar":"Auto-play"})]}),(0,r.jsxs)("div",{className:"p-2 sm:p-4 min-h-[250px] sm:min-h-[300px] max-h-[350px] sm:max-h-[400px] overflow-y-auto",children:[a&&g.map((e,t)=>r.jsx(l.E.div,{initial:P.initial,animate:P.animate,transition:P.transition,className:"mb-4",children:"message"===e.type?r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400 italic",children:e.content}):e.type===eg.ox.TABLE?r.jsx("div",{className:"overflow-auto max-h-[200px] sm:max-h-[250px]",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-700 text-xs sm:text-sm",role:"table","aria-label":"Dados tabulares de exemplo",children:[r.jsx("thead",{className:"bg-gray-50 dark:bg-gray-800",children:r.jsx("tr",{children:Array.isArray(e.content)&&e.content.length>0?e.content[0]?.map((e,t)=>r.jsx("th",{scope:"col",className:"px-2 py-2 sm:px-3 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider",children:e},`header-${t}`)):null})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800",children:Array.isArray(e.content)&&e.content.length>0?e.content.slice(1).map((t,a)=>r.jsx("tr",{className:a%2==0?"bg-white dark:bg-gray-900/80":"bg-gray-50 dark:bg-gray-900/50",children:t.map((t,s)=>r.jsx("td",{className:(0,ef.cn)("px-2 py-2 sm:px-3 sm:py-3 whitespace-nowrap",e.highlightCells?.some(e=>e.row===a+1&&e.col===s)?"bg-blue-100 dark:bg-blue-900/30":""),children:t},s))},a)):null})]})}):e.type===eg.ox.CHART?r.jsx("div",{className:"w-full h-[150px] sm:h-[200px] md:h-[250px] bg-white dark:bg-gray-800 p-2 rounded-md",children:y||O?r.jsx(ej,{data:e.data,chartType:e.chartType}):r.jsx(j.h,{width:"100%",height:"100%",children:"bar"===e.chartType?(0,r.jsxs)(k.v,{data:e.data.datasets[0].data.map((t,a)=>({name:e.data.labels[a],value:t,meta:e.data.datasets[0].label,value2:e.data.datasets[1]?.data[a],meta2:e.data.datasets[1]?.label})),children:[r.jsx(w.q,{strokeDasharray:"3 3",opacity:.2}),r.jsx(N.K,{dataKey:"name",fontSize:10,tick:{fill:"currentColor"},tickFormatter:e=>e.length>5?`${e.substring(0,5)}...`:e}),r.jsx(A.B,{fontSize:10,tick:{fill:"currentColor"}}),r.jsx(S.u,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),r.jsx(C.D,{wrapperStyle:{fontSize:"10px"}}),r.jsx(T.$,{dataKey:"value",name:e.data.datasets[0].label,fill:e.data.datasets[0].backgroundColor,radius:[4,4,0,0]}),e.data.datasets[1]&&r.jsx(T.$,{dataKey:"value2",name:e.data.datasets[1].label,fill:e.data.datasets[1].backgroundColor,radius:[4,4,0,0]})]}):"line"===e.chartType?(0,r.jsxs)(ed,{data:e.data.datasets[0].data.map((t,a)=>({name:e.data.labels[a],value:t,meta:e.data.datasets[0].label,value2:e.data.datasets[1]?.data[a],meta2:e.data.datasets[1]?.label})),children:[r.jsx(w.q,{strokeDasharray:"3 3",opacity:.2}),r.jsx(N.K,{dataKey:"name",fontSize:10,tick:{fill:"currentColor"},tickFormatter:e=>e.length>5?`${e.substring(0,5)}...`:e}),r.jsx(A.B,{fontSize:10,tick:{fill:"currentColor"}}),r.jsx(S.u,{contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),r.jsx(C.D,{wrapperStyle:{fontSize:"10px"}}),r.jsx(el,{type:"monotone",dataKey:"value",name:e.data.datasets[0].label,stroke:e.data.datasets[0].backgroundColor,activeDot:{r:8},strokeWidth:2}),e.data.datasets[1]&&r.jsx(el,{type:"monotone",dataKey:"value2",name:e.data.datasets[1].label,stroke:e.data.datasets[1].backgroundColor,activeDot:{r:8},strokeWidth:2})]}):(0,r.jsxs)(eu.u,{children:[r.jsx(em.b,{data:e.data.datasets[0].data.map((t,a)=>({name:e.data.labels[a],value:t})),cx:"50%",cy:"50%",labelLine:!1,outerRadius:60,fill:"#8884d8",dataKey:"value",nameKey:"name",label:({name:e,percent:t})=>`${e}: ${(100*t).toFixed(0)}%`,children:e.data.datasets[0].data.map((e,t)=>r.jsx(ep.b,{fill:t<eb.length?eb[t]:eb[t%eb.length]},`cell-${t}`))}),r.jsx(S.u,{formatter:e=>[`${e}`,""],contentStyle:{backgroundColor:"rgba(255, 255, 255, 0.9)",border:"1px solid #ccc",borderRadius:"4px",fontSize:"11px"}}),r.jsx(C.D,{wrapperStyle:{fontSize:"10px"}})]})})}):null},t)),!h&&ey[e]?.command&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[r.jsx("div",{className:"h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center",children:r.jsx("span",{className:"text-xs text-white font-medium",children:e+1})}),r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Digite um comando:"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-1 p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-sm font-mono",children:r.jsx(v,{cursor:!0,sequence:[ey[e]?.command||""],wrapper:"span",speed:50,style:{display:"inline-block"},repeat:0,className:"text-gray-800 dark:text-gray-200"})}),r.jsx("button",{className:"ml-2 p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",onClick:()=>M(),"aria-label":"Pr\xf3ximo comando",children:r.jsx(d.Z,{className:"h-4 w-4","aria-hidden":"true"})})]})]}),r.jsx("div",{"aria-live":"polite",className:"sr-only",children:h?"Comando digitado. Processando resultado.":""})]})]})})]})}function ej({data:e,chartType:t}){if(!e||!e.datasets||!e.datasets[0])return r.jsx("div",{className:"flex h-full items-center justify-center",children:r.jsx("p",{className:"text-xs text-gray-500",children:"Dados do gr\xe1fico indispon\xedveis"})});let{labels:a,datasets:s}=e,n=s[0],o=s[1];return(0,r.jsxs)("div",{className:"h-full w-full flex flex-col","aria-label":`Gr\xe1fico de ${t}`,role:"img",children:[r.jsx("div",{className:"text-xs font-medium mb-2 text-center",children:"bar"===t?"Gr\xe1fico de Barras":"line"===t?"Gr\xe1fico de Linha":"Gr\xe1fico de Pizza"}),r.jsx("div",{className:"flex-1 flex items-end space-x-1 px-2 overflow-hidden",children:a.map((e,t)=>{let a=Math.max(...n.data),s=Math.max(10,n.data[t]/a*100);return(0,r.jsxs)("div",{className:"flex flex-col items-center flex-1 min-w-0",children:[r.jsx("div",{className:"w-full relative rounded-t-sm focus:outline-none focus:ring-2 focus:ring-primary",style:{height:`${s}%`,backgroundColor:n.backgroundColor},"aria-label":`${e}: ${n.data[t]}`,tabIndex:0,role:"button",onKeyDown:a=>{("Enter"===a.key||" "===a.key)&&(a.preventDefault(),alert(`${e}: ${n.data[t]}`))}}),r.jsx("div",{className:"text-[8px] mt-1 truncate w-full text-center",title:e,children:e})]},t)})}),(0,r.jsxs)("div",{className:"flex justify-center mt-2 gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx("div",{className:"w-2 h-2",style:{backgroundColor:n.backgroundColor},"aria-hidden":"true"}),r.jsx("span",{className:"text-[9px]",children:n.label})]}),o&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx("div",{className:"w-2 h-2",style:{backgroundColor:o.backgroundColor},"aria-hidden":"true"}),r.jsx("span",{className:"text-[9px]",children:o.label})]})]})]})}},2578:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>d,ZP:()=>u});var r=a(10326),s=a(91470),n=a(37202);let o=(0,a(76557).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var i=a(17577),l=a(51223);let c={error:{className:"bg-destructive/15 text-destructive",iconClassName:"text-destructive",icon:r.jsx(s.Z,{className:"h-5 w-5"})},warning:{className:"bg-warning/15 text-warning",iconClassName:"text-warning",icon:r.jsx(n.Z,{className:"h-5 w-5"})},info:{className:"bg-info/15 text-info",iconClassName:"text-info",icon:r.jsx(o,{className:"h-5 w-5"})},success:{className:"bg-success/15 text-success",iconClassName:"text-success",icon:r.jsx(o,{className:"h-5 w-5"})}},d=i.forwardRef(({type:e="error",message:t,description:a,className:s,iconClassName:n,_iconClassName:o,_icon:i},d)=>{let u=c[e],m=i||u.icon;return(0,r.jsxs)("div",{ref:d,className:(0,l.cn)("flex items-start gap-3 rounded-md border p-3",u.className,s),role:"alert",children:[r.jsx("div",{className:(0,l.cn)("mt-0.5 shrink-0",u.iconClassName,n||o),children:m}),(0,r.jsxs)("div",{className:"grid gap-1",children:[r.jsx("div",{className:"font-medium leading-none tracking-tight",children:t}),a&&r.jsx("div",{className:"text-sm opacity-80",children:a})]})]})});d.displayName="ErrorMessage";let u=d},82015:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var r=a(10326),s=a(17577),n=a(62734);let o=s.forwardRef(({className:e,wrapperClassName:t,variant:a="default",fieldSize:s="md",textareaSize:o,...i},l)=>{let c=r.jsx("textarea",{className:(0,n.RM)(a,o||s,!0,e),ref:l,...i});return(0,n.aF)(c,t)});o.displayName="Textarea";let i=o},56627:(e,t,a)=>{"use strict";a.d(t,{V6:()=>p,pm:()=>x,s6:()=>m});var r=a(17577);let s={ADD_TOAST:"ADD_TOAST",UPDATE_TOAST:"UPDATE_TOAST",DISMISS_TOAST:"DISMISS_TOAST",REMOVE_TOAST:"REMOVE_TOAST"},n=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),u({type:s.REMOVE_TOAST,toastId:e})},1e6);o.set(e,t)},l=(e,t)=>{switch(t.type){case s.ADD_TOAST:return{...e,toasts:[t.toast,...e.toasts].slice(0,5)};case s.UPDATE_TOAST:return{...e,toasts:e.toasts.map(e=>e.id===t.toast?.id?{...e,...t.toast}:e)};case s.DISMISS_TOAST:{let{toastId:a}=t;return a?i(a):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case s.REMOVE_TOAST:if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};default:return e}},c=[],d={toasts:[]};function u(e){d=l(d,e),c.forEach(e=>{e(d)})}function m({...e}){let t=(n=(n+1)%Number.MAX_VALUE).toString(),a=()=>u({type:s.DISMISS_TOAST,toastId:t});return u({type:s.ADD_TOAST,toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>u({type:s.UPDATE_TOAST,toast:{...e,id:t}})}}function p(){let[e,t]=r.useState(d);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{toast:m,dismiss:e=>u({type:s.DISMISS_TOAST,toastId:void 0===e?"":e}),toasts:e.toasts}}let x=p},36478:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var r=a(71159),s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(e,t)=>{let a=(0,r.forwardRef)(({color:a="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,children:c,...d},u)=>(0,r.createElement)("svg",{ref:u,...s,width:o,height:o,stroke:a,strokeWidth:l?24*Number(i)/Number(o):i,className:`lucide lucide-${n(e)}`,...d},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...(Array.isArray(c)?c:[c])||[]]));return a.displayName=`${e}`,a}},44880:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var r=a(19510),s=a(36478);let n=(0,s.Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),o=(0,s.Z)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]]),i=(0,s.Z)("LineChart",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]]),l=(0,s.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),c=(0,s.Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),d=(0,s.Z)("FileSpreadsheet",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M14 17h2",key:"10kma7"}]]),u=(0,s.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var m=a(57371),p=a(71159),x=a(68570);let h=(0,x.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\command-examples-wrapper.tsx`),{__esModule:f,$$typeof:g}=h;h.default;let b=(0,x.createProxy)(String.raw`C:\Users\<USER>\Desktop\do vscode\excel-copilot\src\components\command-examples-wrapper.tsx#CommandExamplesWrapper`);var y=a(27039),v=a(40644);let j=(0,p.lazy)(()=>a.e(7249).then(a.bind(a,7249)).then(e=>({default:e.HeroSection}))),k={blue:{bgFrom:"from-blue-100",bgTo:"to-blue-200",darkFrom:"dark:from-blue-900/50",darkTo:"dark:to-blue-800/30",text:"text-blue-600",darkText:"dark:text-blue-400"},indigo:{bgFrom:"from-indigo-100",bgTo:"to-indigo-200",darkFrom:"dark:from-indigo-900/50",darkTo:"dark:to-indigo-800/30",text:"text-indigo-600",darkText:"dark:text-indigo-400"},purple:{bgFrom:"from-purple-100",bgTo:"to-purple-200",darkFrom:"dark:from-purple-900/50",darkTo:"dark:to-purple-800/30",text:"text-purple-600",darkText:"dark:text-purple-400"},green:{bgFrom:"from-green-100",bgTo:"to-green-200",darkFrom:"dark:from-green-900/50",darkTo:"dark:to-green-800/30",text:"text-green-600",darkText:"dark:text-green-400"},amber:{bgFrom:"from-amber-100",bgTo:"to-amber-200",darkFrom:"dark:from-amber-900/50",darkTo:"dark:to-amber-800/30",text:"text-amber-600",darkText:"dark:text-amber-400"},rose:{bgFrom:"from-rose-100",bgTo:"to-rose-200",darkFrom:"dark:from-rose-900/50",darkTo:"dark:to-rose-800/30",text:"text-rose-600",darkText:"dark:text-rose-400"}};function w(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden bg-gradient-to-b from-blue-50 via-indigo-50/50 to-white dark:from-gray-900 dark:via-blue-950/20 dark:to-background pb-12",children:[(0,r.jsxs)("div",{className:"absolute inset-0 pointer-events-none overflow-hidden",children:[r.jsx("div",{className:"absolute -top-[30%] -left-[10%] w-[60%] h-[60%] rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-600/5 blur-3xl opacity-70"}),r.jsx("div",{className:"absolute -bottom-[20%] right-[10%] w-[40%] h-[40%] rounded-full bg-gradient-to-bl from-indigo-400/20 to-blue-600/5 blur-3xl opacity-70"})]}),r.jsx("div",{className:"container relative mx-auto px-4 pt-16 pb-20 max-w-6xl z-10",children:r.jsx(p.Suspense,{fallback:r.jsx("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx("div",{className:"w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"}),r.jsx("p",{className:"mt-4 text-sm text-muted-foreground",children:"Carregando..."})]})}),children:r.jsx(j,{})})})]}),r.jsx("section",{className:"py-12 bg-gradient-to-b from-white to-blue-50/50 dark:from-background dark:to-blue-950/10",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 max-w-6xl",children:[(0,r.jsxs)("div",{className:"text-center mb-10",children:[r.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-2",children:"Transforme sua experi\xeancia com planilhas"}),r.jsx("p",{className:"text-base text-muted-foreground max-w-2xl mx-auto",children:"IA avan\xe7ada que simplifica sua intera\xe7\xe3o com dados"})]}),r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-12",children:[{icon:r.jsx(n,{className:"h-5 w-5","aria-hidden":"true"}),title:"Linguagem natural",description:"Use comandos em portugu\xeas simples",color:"blue"},{icon:r.jsx(o,{className:"h-5 w-5","aria-hidden":"true"}),title:"Planilhas complexas",description:"Manipule dados facilmente",color:"indigo"},{icon:r.jsx(i,{className:"h-5 w-5","aria-hidden":"true"}),title:"Visualiza\xe7\xf5es",description:"Gr\xe1ficos profissionais r\xe1pidos",color:"purple"},{icon:r.jsx(l,{className:"h-5 w-5","aria-hidden":"true"}),title:"Importa\xe7\xe3o/exporta\xe7\xe3o",description:"Compatibilidade com Excel",color:"green"},{icon:r.jsx(c,{className:"h-5 w-5","aria-hidden":"true"}),title:"F\xf3rmulas inteligentes",description:"Fun\xe7\xf5es avan\xe7adas simplificadas",color:"amber"},{icon:r.jsx(d,{className:"h-5 w-5","aria-hidden":"true"}),title:"Templates prontos",description:"Modelos para iniciar rapidamente",color:"rose"}].map((e,t)=>{let a=k[e.color];return(0,r.jsxs)("div",{className:"flex items-center gap-3 p-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm hover:shadow-sm transition-all focus-within:ring-2 focus-within:ring-primary/70",tabIndex:0,role:"button","aria-label":`Feature: ${e.title} - ${e.description}`,children:[r.jsx("div",{className:(0,v.cn)("p-2 rounded-lg flex items-center justify-center bg-gradient-to-br",a.bgFrom,a.bgTo,a.darkFrom,a.darkTo,a.text,a.darkText),children:e.icon}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-semibold",children:e.title}),r.jsx("p",{className:"text-xs text-muted-foreground hidden xs:block",children:e.description})]})]},t)})}),(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-900/80 rounded-xl p-6 shadow-md border border-gray-200 dark:border-gray-800",children:[r.jsx("h3",{className:"text-xl font-bold mb-4 text-center",children:"Como Funciona"}),r.jsx("div",{className:"grid md:grid-cols-3 gap-4",children:[{step:"1",title:"Descreva o que precisa",description:"Use linguagem natural para pedir o que deseja."},{step:"2",title:"A IA cria em segundos",description:"O Excel Copilot interpreta seu pedido e cria tudo automaticamente."},{step:"3",title:"Personalize e exporte",description:"Ajuste conforme necess\xe1rio e exporte como arquivo Excel."}].map((e,t)=>(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:"flex-shrink-0 flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full shadow-sm text-sm font-bold","aria-hidden":"true",children:e.step}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-sm font-semibold mb-1",children:[(0,r.jsxs)("span",{className:"sr-only",children:["Passo ",e.step,": "]}),e.title]}),r.jsx("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))})]})]})}),r.jsx("section",{className:"py-8 sm:py-12",id:"exemplos","aria-labelledby":"exemplos-titulo",children:(0,r.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 max-w-6xl",children:[(0,r.jsxs)("div",{className:"text-center mb-4 sm:mb-6",children:[r.jsx("h2",{id:"exemplos-titulo",className:"text-xl sm:text-2xl md:text-3xl font-bold mb-1 sm:mb-2",children:"Experimente esses comandos"}),r.jsx("p",{className:"text-sm sm:text-base text-muted-foreground max-w-2xl mx-auto",children:"Veja o poder da linguagem natural em a\xe7\xe3o"})]}),r.jsx("div",{className:"bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-gray-200 dark:border-gray-800 rounded-xl shadow-md p-2 sm:p-4",children:r.jsx(p.Suspense,{fallback:r.jsx("div",{className:"h-[200px] sm:h-[250px] flex items-center justify-center","aria-live":"polite","aria-busy":"true",children:(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 sm:gap-3",children:[r.jsx("div",{className:"w-6 sm:w-8 h-6 sm:h-8 rounded-full border-3 border-blue-200 border-t-blue-600 animate-spin",role:"status"}),r.jsx("p",{className:"text-xs sm:text-sm text-muted-foreground",children:"Carregando exemplos..."})]})}),children:r.jsx(b,{})})}),r.jsx("div",{className:"mt-6 sm:mt-8 bg-gradient-to-br from-blue-600 to-indigo-700 dark:from-blue-700 dark:to-indigo-900 rounded-xl overflow-hidden shadow-lg p-4 sm:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center gap-3 sm:gap-4",children:[r.jsx("div",{className:"p-2 sm:p-3 bg-white/20 backdrop-blur-sm rounded-full","aria-hidden":"true",children:r.jsx(d,{className:"h-6 w-6 sm:h-8 sm:w-8 text-white"})}),(0,r.jsxs)("div",{className:"text-center md:text-left flex-1",children:[r.jsx("h2",{className:"text-lg sm:text-xl md:text-2xl font-bold text-white mb-2",children:"Comece sua jornada agora"}),r.jsx("p",{className:"text-xs sm:text-sm text-blue-100 mb-3 sm:mb-4 max-w-2xl",children:"Pare de perder tempo com f\xf3rmulas complexas. Use o poder da IA agora."}),r.jsx(y.z,{className:"rounded-full bg-white hover:bg-gray-100 text-blue-600 px-4 sm:px-5 py-1.5 sm:py-2 text-xs sm:text-sm font-medium border-0 shadow-md focus:ring-2 focus:ring-white/70 focus:ring-offset-2 focus:ring-offset-blue-600",asChild:!0,children:(0,r.jsxs)(m.default,{href:"/dashboard",children:["Criar minha primeira planilha"," ",r.jsx(u,{className:"ml-1 h-3 w-3 sm:h-4 sm:w-4","aria-hidden":"true"})]})})]})]})})]})})]})}},37125:(e,t,a)=>{"use strict";function r(e,[t,a]){return Math.min(a,Math.max(t,e))}a.d(t,{u:()=>r})},6009:(e,t,a)=>{"use strict";a.d(t,{C2:()=>o,TX:()=>i,fC:()=>l});var r=a(17577),s=a(45226),n=a(10326),o=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),i=r.forwardRef((e,t)=>(0,n.jsx)(s.WV.span,{...e,ref:t,style:{...o,...e.style}}));i.displayName="VisuallyHidden";var l=i}};var t=require("../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,9557,7410,86,7915,6068,615,2972,4433,6841,6643],()=>a(63021));module.exports=r})();