import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';

export interface UserProfile {
  id: string;
  name: string | null;
  email: string | null;
  image: string | null;
  emailVerified: Date | null;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt: Date | null;
  loginCount: number;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: 'pt-BR' | 'en-US';
    notifications: {
      email: boolean;
      push: boolean;
      marketing: boolean;
    };
    privacy: {
      profileVisible: boolean;
      shareUsageData: boolean;
    };
  };
  stats: {
    workbooksCount: number;
    memberSince: Date;
    lastLogin: Date | null;
    loginCount: number;
  };
  subscription: {
    plan: string;
    status: string;
    currentPeriodEnd: Date;
  } | null;
}

export function useProfile() {
  const { data: session } = useSession();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Função para carregar o perfil
  const fetchProfile = async () => {
    if (!session?.user) {
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/user/profile');
      const data = await response.json();

      if (response.ok) {
        setProfile(data.profile);
      } else {
        setError(data.error || 'Erro ao carregar perfil');
        toast.error('Erro ao carregar perfil: ' + data.error);
      }
    } catch (err) {
      const errorMessage = 'Erro ao carregar perfil';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Função para atualizar o perfil
  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!profile) return false;

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      const result = await response.json();

      if (response.ok) {
        setProfile(prev => prev ? { ...prev, ...result.profile } : null);
        toast.success('Perfil atualizado com sucesso!');
        return true;
      } else {
        toast.error('Erro ao atualizar perfil: ' + result.error);
        return false;
      }
    } catch (error) {
      toast.error('Erro ao atualizar perfil');
      return false;
    }
  };

  // Função específica para atualizar avatar
  const updateAvatar = async (imageUrl: string) => {
    return updateProfile({ image: imageUrl });
  };

  // Função específica para atualizar preferências
  const updatePreferences = async (preferences: UserProfile['preferences']) => {
    return updateProfile({ preferences });
  };

  // Carregar perfil quando a sessão estiver disponível
  useEffect(() => {
    if (session) {
      fetchProfile();
    } else {
      setLoading(false);
    }
  }, [session]);

  return {
    profile,
    loading,
    error,
    updateProfile,
    updateAvatar,
    updatePreferences,
    refetch: fetchProfile,
  };
}
