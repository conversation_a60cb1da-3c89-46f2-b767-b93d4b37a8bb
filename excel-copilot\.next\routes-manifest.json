{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/api/:path*", "has": [{"type": "header", "key": "x-legacy-api", "value": "(?<legacyValue>.*)"}], "destination": "/api/legacy-redirect/:path*", "statusCode": 307, "regex": "^(?!/_next)/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "headers": [{"source": "/:path*", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://apis.google.com https://js.stripe.com https://accounts.google.com https://github.com https://va.vercel-scripts.com; script-src-elem 'self' 'unsafe-inline' https://js.stripe.com https://cdn.jsdelivr.net https://apis.google.com https://accounts.google.com https://github.com https://va.vercel-scripts.com; worker-src 'self' blob: data:; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com; img-src 'self' data: https: blob:; font-src 'self' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com data:; connect-src 'self' https://api.openai.com https://api.stripe.com https://*.vercel.app https://*.googleapis.com https://*.google.com https://github.com https://accounts.google.com https://va.vercel-scripts.com https://vitals.vercel-insights.com https://*.supabase.co; frame-src 'self' https://js.stripe.com https://accounts.google.com https://github.com; object-src 'none'; base-uri 'self'; form-action 'self' https://accounts.google.com https://github.com; frame-ancestors 'self'; upgrade-insecure-requests;"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=(), interest-cohort=()"}], "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/excel/download/[id]", "regex": "^/api/excel/download/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/excel/download/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/legacy-redirect/[...path]", "regex": "^/api/legacy\\-redirect/(.+?)(?:/)?$", "routeKeys": {"nxtPpath": "nxtPpath"}, "namedRegex": "^/api/legacy\\-redirect/(?<nxtPpath>.+?)(?:/)?$"}, {"page": "/api/trpc/[trpc]", "regex": "^/api/trpc/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtrpc": "nxtPtrpc"}, "namedRegex": "^/api/trpc/(?<nxtPtrpc>[^/]+?)(?:/)?$"}, {"page": "/api/workbooks/[id]", "regex": "^/api/workbooks/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workbooks/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/workbooks/[id]/collaborators", "regex": "^/api/workbooks/([^/]+?)/collaborators(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workbooks/(?<nxtPid>[^/]+?)/collaborators(?:/)?$"}, {"page": "/api/workbooks/[id]/duplicate", "regex": "^/api/workbooks/([^/]+?)/duplicate(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workbooks/(?<nxtPid>[^/]+?)/duplicate(?:/)?$"}, {"page": "/api/workbooks/[id]/export", "regex": "^/api/workbooks/([^/]+?)/export(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workbooks/(?<nxtPid>[^/]+?)/export(?:/)?$"}, {"page": "/api/workbooks/[id]/sheets/[sheetId]/chunks", "regex": "^/api/workbooks/([^/]+?)/sheets/([^/]+?)/chunks(?:/)?$", "routeKeys": {"nxtPid": "nxtPid", "nxtPsheetId": "nxtPsheetId"}, "namedRegex": "^/api/workbooks/(?<nxtPid>[^/]+?)/sheets/(?<nxtPsheetId>[^/]+?)/chunks(?:/)?$"}, {"page": "/api/workbooks/[id]/storage", "regex": "^/api/workbooks/([^/]+?)/storage(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/workbooks/(?<nxtPid>[^/]+?)/storage(?:/)?$"}, {"page": "/workbook/[id]", "regex": "^/workbook/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/workbook/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/health", "regex": "^/admin/health(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/health(?:/)?$"}, {"page": "/api-docs", "regex": "^/api\\-docs(?:/)?$", "routeKeys": {}, "namedRegex": "^/api\\-docs(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/account", "regex": "^/dashboard/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/account(?:/)?$"}, {"page": "/dashboard/analytics", "regex": "^/dashboard/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analytics(?:/)?$"}, {"page": "/examples", "regex": "^/examples(?:/)?$", "routeKeys": {}, "namedRegex": "^/examples(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/pricing", "regex": "^/pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/pricing(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/templates", "regex": "^/templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/templates(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/workbook/new", "regex": "^/workbook/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/workbook/new(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}