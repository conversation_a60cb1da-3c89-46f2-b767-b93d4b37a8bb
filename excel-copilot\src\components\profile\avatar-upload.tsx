'use client';

import { useState, useRef, ChangeEvent } from 'react';
import { toast } from 'sonner';
import { Camera, Upload, Loader2, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface AvatarUploadProps {
  currentImage?: string | null;
  userName?: string | null;
  onImageUpdate: (imageUrl: string) => void;
  className?: string;
}

export function AvatarUpload({ 
  currentImage, 
  userName, 
  onImageUpdate, 
  className 
}: AvatarUploadProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [imageUrl, setImageUrl] = useState(currentImage || '');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getInitials = (name?: string | null) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Função para validar arquivo de imagem
  const validateImageFile = (file: File): boolean => {
    // Verificar tipo de arquivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Formato não suportado. Use JPEG, PNG ou WebP.');
      return false;
    }

    // Verificar tamanho (máximo 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast.error('Arquivo muito grande. Máximo 5MB.');
      return false;
    }

    return true;
  };

  // Função para fazer upload do arquivo
  const handleFileUpload = async (file: File) => {
    if (!validateImageFile(file)) return;

    setUploading(true);
    try {
      // Criar preview local
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Simular upload para um serviço de imagens
      // Em produção, você integraria com Supabase Storage, Cloudinary, etc.
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Por enquanto, usar um serviço de placeholder
      const placeholderUrl = `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(userName || 'User')}&backgroundColor=random`;
      
      setImageUrl(placeholderUrl);
      toast.success('Avatar atualizado com sucesso!');
      
      // Chamar callback para atualizar no componente pai
      onImageUpdate(placeholderUrl);
      
      setIsOpen(false);
    } catch (error) {
      toast.error('Erro ao fazer upload da imagem');
    } finally {
      setUploading(false);
      setPreviewUrl(null);
    }
  };

  // Função para lidar com mudança de arquivo
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  // Função para atualizar URL manualmente
  const handleUrlUpdate = async () => {
    if (!imageUrl.trim()) {
      toast.error('Digite uma URL válida');
      return;
    }

    try {
      // Validar se a URL é uma imagem válida
      const img = new Image();
      img.onload = () => {
        onImageUpdate(imageUrl);
        toast.success('Avatar atualizado com sucesso!');
        setIsOpen(false);
      };
      img.onerror = () => {
        toast.error('URL de imagem inválida');
      };
      img.src = imageUrl;
    } catch (error) {
      toast.error('Erro ao validar imagem');
    }
  };

  // Função para remover avatar
  const handleRemoveAvatar = () => {
    setImageUrl('');
    onImageUpdate('');
    toast.success('Avatar removido');
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className={`relative group cursor-pointer ${className}`}>
          <Avatar className="h-20 w-20">
            <AvatarImage src={currentImage || ''} alt={userName || 'Usuário'} />
            <AvatarFallback className="text-lg">{getInitials(userName)}</AvatarFallback>
          </Avatar>
          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Camera className="h-6 w-6 text-white" />
          </div>
        </div>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Atualizar Avatar</DialogTitle>
          <DialogDescription>
            Faça upload de uma nova imagem ou cole uma URL
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Preview atual */}
          <div className="flex justify-center">
            <Avatar className="h-24 w-24">
              <AvatarImage 
                src={previewUrl || currentImage || ''} 
                alt={userName || 'Usuário'} 
              />
              <AvatarFallback className="text-xl">
                {getInitials(userName)}
              </AvatarFallback>
            </Avatar>
          </div>

          {/* Upload de arquivo */}
          <div className="space-y-2">
            <Label>Fazer Upload</Label>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploading}
                className="flex-1"
              >
                {uploading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Upload className="h-4 w-4 mr-2" />
                )}
                {uploading ? 'Enviando...' : 'Escolher Arquivo'}
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              JPEG, PNG ou WebP. Máximo 5MB.
            </p>
          </div>

          {/* URL manual */}
          <div className="space-y-2">
            <Label htmlFor="imageUrl">URL da Imagem</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="imageUrl"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                placeholder="https://exemplo.com/imagem.jpg"
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={handleUrlUpdate}
                disabled={!imageUrl.trim()}
              >
                Aplicar
              </Button>
            </div>
          </div>

          {/* Ações */}
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={handleRemoveAvatar}
              className="text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4 mr-2" />
              Remover
            </Button>
            
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancelar
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
