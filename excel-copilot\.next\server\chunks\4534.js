exports.id=4534,exports.ids=[4534,4349],exports.modules={33261:(e,a,s)=>{"use strict";s.d(a,{Cd:()=>c,X:()=>d,bZ:()=>i});var r=s(10326),t=s(79360),o=s(17577),l=s(51223);let n=(0,t.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-500/50 text-yellow-600 dark:border-yellow-500/30 dark:text-yellow-500 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-500"}},defaultVariants:{variant:"default"}}),i=o.forwardRef(({className:e,variant:a,...s},t)=>r.jsx("div",{ref:t,role:"alert",className:(0,l.cn)(n({variant:a}),e),...s}));i.displayName="Alert";let c=o.forwardRef(({className:e,...a},s)=>r.jsx("h5",{ref:s,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...a}));c.displayName="AlertTitle";let d=o.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...a}));d.displayName="AlertDescription"},24118:(e,a,s)=>{"use strict";s.d(a,{$N:()=>x,Be:()=>f,Vq:()=>i,cN:()=>h,cZ:()=>m,fK:()=>p,hg:()=>c});var r=s(10326),t=s(98958),o=s(94019),l=s(17577),n=s(51223);let i=t.fC,c=t.xz,d=t.h_;t.x8;let u=l.forwardRef(({className:e,...a},s)=>r.jsx(t.aV,{ref:s,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));u.displayName=t.aV.displayName;let m=l.forwardRef(({className:e,children:a,...s},l)=>(0,r.jsxs)(d,{children:[r.jsx(u,{}),(0,r.jsxs)(t.VY,{ref:l,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[a,(0,r.jsxs)(t.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[r.jsx(o.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=t.VY.displayName;let p=({className:e,...a})=>r.jsx("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});p.displayName="DialogHeader";let h=({className:e,...a})=>r.jsx("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});h.displayName="DialogFooter";let x=l.forwardRef(({className:e,...a},s)=>r.jsx(t.Dx,{ref:s,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));x.displayName=t.Dx.displayName;let f=l.forwardRef(({className:e,...a},s)=>r.jsx(t.dk,{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...a}));f.displayName=t.dk.displayName},44794:(e,a,s)=>{"use strict";s.d(a,{_:()=>c});var r=s(10326),t=s(34478),o=s(79360),l=s(17577),n=s(51223);let i=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef(({className:e,...a},s)=>r.jsx(t.f,{ref:s,className:(0,n.cn)(i(),e),...a}));c.displayName=t.f.displayName},29280:(e,a,s)=>{"use strict";s.d(a,{Bw:()=>x,Ph:()=>d,Ql:()=>f,i4:()=>m,ki:()=>u});var r=s(10326),t=s(44875),o=s(941),l=s(96633),n=s(32933),i=s(17577),c=s(51223);let d=t.fC;t.ZA;let u=t.B4,m=i.forwardRef(({className:e,children:a,...s},l)=>(0,r.jsxs)(t.xz,{ref:l,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,r.jsx(t.JO,{asChild:!0,children:r.jsx(o.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=t.xz.displayName;let p=i.forwardRef(({className:e,...a},s)=>r.jsx(t.u_,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(l.Z,{className:"h-4 w-4"})}));p.displayName=t.u_.displayName;let h=i.forwardRef(({className:e,...a},s)=>r.jsx(t.$G,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(o.Z,{className:"h-4 w-4"})}));h.displayName=t.$G.displayName;let x=i.forwardRef(({className:e,children:a,position:s="popper",...o},l)=>r.jsx(t.h_,{children:(0,r.jsxs)(t.VY,{ref:l,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...o,children:[r.jsx(p,{}),r.jsx(t.l_,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),r.jsx(h,{})]})}));x.displayName=t.VY.displayName,i.forwardRef(({className:e,...a},s)=>r.jsx(t.__,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=t.__.displayName;let f=i.forwardRef(({className:e,children:a,...s},o)=>(0,r.jsxs)(t.ck,{ref:o,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(t.wU,{children:r.jsx(n.Z,{className:"h-4 w-4"})})}),r.jsx(t.eT,{children:a})]}));f.displayName=t.ck.displayName,i.forwardRef(({className:e,...a},s)=>r.jsx(t.Z0,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=t.Z0.displayName},50384:(e,a,s)=>{"use strict";s.d(a,{SP:()=>c,dr:()=>i,mQ:()=>n,nU:()=>d});var r=s(10326),t=s(28407),o=s(17577),l=s(51223);let n=t.fC,i=o.forwardRef(({className:e,...a},s)=>r.jsx(t.aV,{ref:s,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));i.displayName=t.aV.displayName;let c=o.forwardRef(({className:e,...a},s)=>r.jsx(t.xz,{ref:s,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));c.displayName=t.xz.displayName;let d=o.forwardRef(({className:e,...a},s)=>r.jsx(t.VY,{ref:s,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));d.displayName=t.VY.displayName},37568:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[31mx\x1b[0m Expected ',', got ':'\n     ,-[\x1b[36;1;4mC:\\Users\\<USER>\\Desktop\\do vscode\\excel-copilot\\src\\components\\upload-button.tsx\x1b[0m:161:1]\n \x1b[2m161\x1b[0m |             progressTracker.completeStep('data-transformation');\n \x1b[2m162\x1b[0m |             progressTracker.startStep('finalization', 'Finalizando...');\n \x1b[2m163\x1b[0m |           }\n \x1b[2m164\x1b[0m |         } : {}),\n     : \x1b[31;1m          ^\x1b[0m\n \x1b[2m165\x1b[0m |       });\n \x1b[2m166\x1b[0m |     } catch (error) {\n \x1b[2m166\x1b[0m |       console.error('Erro no upload:', error);\r\n     `----\n\n\nCaused by:\n    Syntax Error")},24534:(e,a,s)=>{"use strict";s.d(a,{SpreadsheetEditor:()=>aD});var r,t=s(10326),o=s(25596),l=s(99102),n=s(24230),i=s(95396),c=s(1572),d=s(94019),u=s(23133),m=s(99316),p=s(75290),h=s(31215),x=s(11890),f=s(39183),g=s(75126),y=s(227),b=s(40617),v=s(87888),w=s(35047),A=s(17577),N=s(85999),j=s(88307),C=s(70717),E=s(69436),S=s(91664),F=s(74964),k=s(51223);let O=F.fC,R=F.xz,I=A.forwardRef(({className:e,align:a="center",sideOffset:s=4,...r},o)=>t.jsx(F.h_,{children:t.jsx(F.VY,{ref:o,align:a,sideOffset:s,className:(0,k.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));I.displayName=F.VY.displayName;var T=s(82631),_=s(50337),D=s(72257),$=s(41137),L=s(59368),M=s(4198),Z=s(73078),z=s(38443),P=s(3236);let B=[{id:"soma",command:"/soma",description:"Soma valores em um intervalo de c\xe9lulas",example:"/soma A1:A10",category:"calculation",icon:t.jsx(D.Z,{className:"h-4 w-4 text-blue-500"})},{id:"media",command:"/media",description:"Calcula a m\xe9dia de um intervalo de c\xe9lulas",example:"/media B1:B10",category:"calculation",icon:t.jsx(D.Z,{className:"h-4 w-4 text-blue-500"})},{id:"maximo",command:"/maximo",description:"Encontra o valor m\xe1ximo em um intervalo",example:"/maximo C1:C20",category:"calculation",icon:t.jsx(D.Z,{className:"h-4 w-4 text-blue-500"})},{id:"grafico",command:"/grafico",description:"Cria um gr\xe1fico com os dados selecionados",example:'/grafico tipo="barras" dados=A1:B10',category:"visualization",icon:t.jsx(i.Z,{className:"h-4 w-4 text-green-500"})},{id:"pizza",command:"/grafico-pizza",description:"Cria um gr\xe1fico de pizza",example:'/grafico-pizza dados=C1:D10 titulo="Vendas por Regi\xe3o"',category:"visualization",icon:t.jsx(i.Z,{className:"h-4 w-4 text-green-500"})},{id:"formatar",command:"/formatar",description:"Formata c\xe9lulas selecionadas",example:'/formatar A1:C10 negrito cor="azul"',category:"formatting",icon:t.jsx(c.Z,{className:"h-4 w-4 text-purple-500"})},{id:"condicional",command:"/formato-condicional",description:"Aplica formata\xe7\xe3o condicional",example:'/formato-condicional A1:A10 maior=100 cor="verde"',category:"formatting",icon:t.jsx(c.Z,{className:"h-4 w-4 text-purple-500"})},{id:"filtrar",command:"/filtrar",description:"Filtra dados com base em crit\xe9rios",example:'/filtrar coluna="Vendas" valor>1000',category:"filter",icon:t.jsx($.Z,{className:"h-4 w-4 text-amber-500"})},{id:"ordenar",command:"/ordenar",description:"Ordena dados de uma coluna",example:'/ordenar coluna="Data" crescente=true',category:"filter",icon:t.jsx(L.Z,{className:"h-4 w-4 text-amber-500"})},{id:"tabela",command:"/tabela",description:"Converte intervalo em tabela formatada",example:'/tabela A1:D10 nome="MinhaTabela"',category:"data",icon:t.jsx(M.Z,{className:"h-4 w-4 text-red-500"})},{id:"inserir",command:"/inserir",description:"Insere novas linhas ou colunas",example:"/inserir linhas=5 posicao=A10",category:"data",icon:t.jsx(l.Z,{className:"h-4 w-4 text-red-500"})}];function V({onSelect:e,onClose:a}){let[s,r]=(0,A.useState)(""),[o,l]=(0,A.useState)(B),[n,u]=(0,A.useState)(0),[m,p]=(0,A.useState)("all"),h=(0,A.useRef)(null),x=(0,A.useRef)(null),f=s=>{"string"==typeof s&&(e(s),a())},g=[{id:"all",label:"Todos",icon:t.jsx(j.Z,{className:"h-4 w-4"})},{id:"calculation",label:"C\xe1lculos",icon:t.jsx(D.Z,{className:"h-4 w-4"})},{id:"visualization",label:"Gr\xe1ficos",icon:t.jsx(i.Z,{className:"h-4 w-4"})},{id:"formatting",label:"Formata\xe7\xe3o",icon:t.jsx(c.Z,{className:"h-4 w-4"})},{id:"filter",label:"Filtros",icon:t.jsx($.Z,{className:"h-4 w-4"})},{id:"data",label:"Dados",icon:t.jsx(M.Z,{className:"h-4 w-4"})}];return(0,t.jsxs)("div",{ref:x,className:"absolute bottom-full left-0 w-full max-w-md bg-background border border-input rounded-md shadow-md z-50 mb-2 overflow-hidden",role:"dialog","aria-label":"Paleta de comandos",children:[(0,t.jsxs)("div",{className:"flex items-center p-2 border-b",children:[t.jsx(Z.Z,{className:"h-4 w-4 text-muted-foreground mr-2"}),t.jsx("input",{ref:h,type:"text",value:s,onChange:e=>r(e.target.value),onKeyDown:e=>{o&&0!==o.length&&("ArrowDown"===e.key?(e.preventDefault(),u(e=>(e+1)%o.length)):"ArrowUp"===e.key?(e.preventDefault(),u(e=>(e-1+o.length)%o.length)):"Enter"===e.key?(e.preventDefault(),n>=0&&n<o.length&&o[n]&&"string"==typeof o[n].command&&f(o[n].command)):"Escape"===e.key&&(e.preventDefault(),a()))},placeholder:"Pesquisar comandos...",className:"flex-1 bg-transparent outline-none text-sm","aria-label":"Pesquisar comandos"}),t.jsx("button",{onClick:()=>a(),className:"h-6 w-6 flex items-center justify-center rounded-sm hover:bg-muted","aria-label":"Fechar paleta de comandos",children:t.jsx(d.Z,{className:"h-4 w-4 text-muted-foreground"})})]}),t.jsx("div",{className:"flex items-center gap-1 p-2 overflow-x-auto border-b scrollbar-hide",children:g.map(e=>(0,t.jsxs)(z.C,{variant:m===e.id?"default":"outline",className:"cursor-pointer px-2 py-1 flex items-center gap-1",onClick:()=>p(e.id),children:[e.icon,t.jsx("span",{children:e.label})]},e.id))}),t.jsx(P.x,{className:"max-h-[300px]",children:t.jsx("div",{className:"py-1",role:"listbox",children:o.length>0?o.map((e,a)=>t.jsx("div",{className:`px-3 py-2 text-sm cursor-pointer transition-colors ${a===n?"bg-muted":"hover:bg-muted/50"}`,onClick:()=>f(e.command),onMouseEnter:()=>u(a),role:"option","aria-selected":a===n,tabIndex:-1,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"font-medium",children:e.command}),t.jsx("kbd",{className:"text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground",children:"Enter ↵"})]}),t.jsx("p",{className:"text-xs text-muted-foreground",children:e.description}),t.jsx("p",{className:"text-xs italic mt-0.5 text-muted-foreground",children:e.example})]})]})},e.id)):t.jsx("div",{className:"px-3 py-4 text-sm text-center text-muted-foreground",children:"Nenhum comando encontrado"})})})]})}function U({onSendMessage:e,isLoading:a=!1,placeholder:s="Digite um comando...",disabled:r=!1,showExamples:o=!0,autoFocus:l=!0,className:i="",onChange:d}){let[u,m]=(0,A.useState)(""),[h,x]=(0,A.useState)(!1),[f,g]=(0,A.useState)(!1),[y,b]=(0,A.useState)(!1),v=(0,A.useRef)(null),[w,F]=(0,A.useState)(()=>[]),[D,$]=(0,A.useState)(-1),L=async s=>{s&&s.preventDefault();let t=u.trim();if(t&&!a&&!r){F(e=>{if(!Array.isArray(e))return[t];let a=e.filter(e=>e!==t);return[t,...a].slice(0,10)}),m(""),d&&"function"==typeof d&&d(""),h&&x(!1);try{await e(t)}catch(e){console.error("Erro ao enviar mensagem:",e),N.toast.error("Erro ao enviar comando",{description:"N\xe3o foi poss\xedvel processar seu comando. Tente novamente."})}v.current&&v.current.focus(),$(-1)}},M=e=>{m(e),b(!1),v.current&&v.current.focus()},Z=[{text:"Somar valores da coluna B",icon:t.jsx(n.Z,{className:"h-3 w-3"}),category:"calc"},{text:"Criar gr\xe1fico de vendas por regi\xe3o",icon:t.jsx(c.Z,{className:"h-3 w-3"}),category:"visual"},{text:"Filtrar valores maiores que 100",icon:t.jsx(j.Z,{className:"h-3 w-3"}),category:"filter"},{text:"Formatar c\xe9lulas como moeda",icon:t.jsx(c.Z,{className:"h-3 w-3"}),category:"format"},{text:"Ordenar coluna A em ordem alfab\xe9tica",icon:t.jsx(n.Z,{className:"h-3 w-3"}),category:"order"}];return(0,t.jsxs)("div",{className:`relative w-full ${i}`,children:[h&&t.jsx(V,{onSelect:e=>{m(e),x(!1),v.current&&v.current.focus()},onClose:()=>x(!1)}),(0,t.jsxs)("form",{onSubmit:L,className:"flex items-center gap-2 w-full",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[t.jsx("input",{ref:v,className:(0,k.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","pr-10",_.z6.radius.md,f?"border-primary":void 0),placeholder:a?"Processando comando...":s,value:u,onChange:e=>{m(e.target.value),d&&d(e.target.value)},onKeyDown:e=>{if("Enter"===e.key&&!e.shiftKey){e.preventDefault(),L();return}if("ArrowUp"===e.key&&!h){if((""===u||0===e.currentTarget.selectionStart)&&Array.isArray(w)&&w.length>0){e.preventDefault();let a=D<w.length-1?D+1:w.length-1;a>=0&&a<w.length&&($(a),w[a]&&m(w[a]))}return}if("ArrowDown"===e.key&&!h&&Array.isArray(w)){if(e.preventDefault(),D>0){let e=D-1;$(e),e>=0&&e<w.length&&w[e]&&m(w[e])}else 0===D&&($(-1),m(""));return}if("/"===e.key&&""===u){e.preventDefault(),x(!0);return}if("Escape"===e.key&&h){e.preventDefault(),x(!1);return}},disabled:a||r,autoFocus:l,"aria-label":"Digite seu comando para a planilha"}),a&&t.jsx("div",{className:"absolute right-3 top-1/2 -translate-y-1/2 text-primary",children:t.jsx(p.Z,{className:"h-4 w-4 animate-spin"})})]}),t.jsx(T.pn,{children:(0,t.jsxs)(T.u,{children:[t.jsx(T.aJ,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsxs)(O,{open:y,onOpenChange:b,children:[t.jsx(R,{asChild:!0,children:t.jsx(S.Button,{type:"button",size:"icon",variant:"outline",disabled:0===w.length,className:"shrink-0",children:t.jsx(C.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(I,{className:"w-72 p-0",align:"end",children:[t.jsx("div",{className:"text-sm font-medium p-3 border-b",children:"Comandos recentes"}),t.jsx("div",{className:"max-h-[200px] overflow-y-auto",children:w.map((e,a)=>t.jsx("div",{onClick:()=>M(e),className:"p-2 hover:bg-muted cursor-pointer text-sm truncate px-3",children:e},a))})]})]}),t.jsx(S.Button,{type:"submit",size:"icon",variant:u.trim()?"default":"secondary",disabled:!u.trim()||a||r,"aria-label":"Enviar comando",className:"transition-all duration-300 shrink-0",children:a?t.jsx(p.Z,{className:"h-4 w-4 animate-spin"}):t.jsx(E.Z,{className:"h-4 w-4"})})]})}),t.jsx(T._v,{children:t.jsx("p",{children:"Enviar comando (Enter)"})})]})})]}),o&&!u&&!h&&0===w.length&&t.jsx("div",{className:"flex flex-wrap gap-1 mt-2",children:Z.map((e,a)=>(0,t.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-7 text-xs",onClick:()=>m(e.text),children:[e.icon,t.jsx("span",{className:"ml-1",children:e.text})]},a))})]})}var q=s(59593),H=s(687),X=s(29752);function G({commandId:e,command:a,onDismiss:s,onFeedbackSubmit:r}){let[o,l]=(0,A.useState)(null),[n,i]=(0,A.useState)(""),[c,u]=(0,A.useState)(!1),[m,p]=(0,A.useState)(!1),h=async e=>{l(e),u(!0),e&&!c&&await x(e,"")},x=async(t,o)=>{try{p(!0),await r({commandId:e,command:a,successful:t,feedbackText:o}),N.toast.success("Feedback enviado",{description:"Obrigado por ajudar a melhorar nosso sistema!"}),s()}catch(e){console.error("Erro ao enviar feedback:",e),N.toast.error("N\xe3o foi poss\xedvel enviar o feedback")}finally{p(!1)}},f=async()=>{null!==o&&await x(o,n)};return t.jsx(X.Zb,{className:"p-3 mb-3 border border-gray-200 dark:border-gray-800",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm font-medium text-slate-600 dark:text-slate-300",children:"O comando funcionou como esperado?"}),t.jsx(S.Button,{variant:"ghost",size:"sm",onClick:s,className:"h-6 w-6 p-0 rounded-full",children:t.jsx(d.Z,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(S.Button,{variant:!0===o?"default":"outline",size:"sm",onClick:()=>h(!0),className:!0===o?"bg-green-600 hover:bg-green-700":"",disabled:m,children:[t.jsx(q.Z,{className:"h-4 w-4 mr-1"}),"Sim"]}),(0,t.jsxs)(S.Button,{variant:!1===o?"default":"outline",size:"sm",onClick:()=>h(!1),className:!1===o?"bg-red-600 hover:bg-red-700":"",disabled:m,children:[t.jsx(H.Z,{className:"h-4 w-4 mr-1"}),"N\xe3o"]})]}),c&&(0,t.jsxs)("div",{className:"mt-2 space-y-2",children:[t.jsx("textarea",{className:(0,k.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50","resize-vertical"),value:n,onChange:e=>i(e.target.value),placeholder:"Descreva o que voc\xea gostaria que o comando fizesse...",id:"feedback-text"}),t.jsx("div",{className:"flex justify-end",children:(0,t.jsxs)(S.Button,{variant:"default",size:"sm",onClick:f,disabled:m,className:"flex items-center",children:[m?"Enviando...":"Enviar",t.jsx(E.Z,{className:"h-3 w-3 ml-1"})]})})]})]})})}var Y=s(91470),Q=s(94893);function J({command:e,interpretation:a,isLoading:s,onExecute:r,onCancel:o}){let[l,n]=(0,A.useState)(!1);return l?t.jsx(X.Zb,{className:"p-4 mb-3 border border-blue-200 dark:border-blue-900 bg-blue-50 dark:bg-blue-950/30",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[t.jsx("div",{className:"text-sm font-medium",children:t.jsx("span",{className:"text-blue-600 dark:text-blue-400",children:"Interpreta\xe7\xe3o do comando:"})}),t.jsx("p",{className:"text-sm text-slate-700 dark:text-slate-300",children:a}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 mt-2",children:[(0,t.jsxs)(S.Button,{variant:"outline",size:"sm",onClick:o,className:"flex items-center",disabled:s,children:[t.jsx(Y.Z,{className:"h-4 w-4 mr-1"}),"Cancelar"]}),(0,t.jsxs)(S.Button,{variant:"default",size:"sm",onClick:r,className:"flex items-center bg-green-600 hover:bg-green-700",disabled:s,children:[s?t.jsx(v.Z,{className:"h-4 w-4 mr-1 animate-pulse"}):t.jsx(Q.Z,{className:"h-4 w-4 mr-1"}),s?"Executando...":"Executar"]})]})]})}):null}var W=s(31540),K=s(88378),ee=s(48705),ea=s(10143),es=s(23532),er=s(35342);function et(e,a,s=""){if(!e||!Array.isArray(e)||a<0||a>=e.length)return s;let r=e[a];return void 0!==r?r:s}var eo=s(96671);function el(e){return{...e,id:e.id||`op_${Date.now()}_${Math.random().toString(36).substring(2,9)}`}}async function en(e,a){try{if("ADVANCED_VISUALIZATION"!==a.type||!a.data)throw Error("Opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada inv\xe1lida");let s=a.data,r=s.sourceRange,t=await ei(e,r),o=s.destinationRange||function(e){let a=Object.keys(e._visualizations||{}).length,s=String.fromCharCode(65+a%3*8);return`${s}${15*Math.floor(a/3)+1}`}(e),l=s.id||"viz_"+Math.random().toString(36).substring(2,9);return e._visualizations||(e._visualizations={}),e._visualizations[l]={type:s.type,title:s.title,data:t,config:s,position:o},{updatedData:e,resultSummary:`Visualiza\xe7\xe3o avan\xe7ada "${s.title||s.type}" criada com sucesso em ${o}`}}catch(a){return console.error("Erro ao executar opera\xe7\xe3o de visualiza\xe7\xe3o avan\xe7ada:",a),{updatedData:e,resultSummary:`Erro ao criar visualiza\xe7\xe3o avan\xe7ada: ${a.message}`}}}async function ei(e,a){try{if(Array.isArray(e)&&e.length>0)return e;if("object"==typeof e&&!Array.isArray(e)){let s=[],r=a.split(":"),t=r[0],o=r.length>1?r[1]:t;if(!t)return[];let l=t.match(/[A-Z]+/),n=t.match(/\d+/),i=o?o.match(/[A-Z]+/):null,c=o?o.match(/\d+/):null,d=l?l[0]:"A",u=n?parseInt(n[0],10):1,m=i&&i[0]?i[0]:d,p=c&&c[0]?parseInt(c[0],10):u;if(u<=0||p<=0)return[];let h=(0,k.WH)(d),x=(0,k.WH)(m),f=[];for(let a=h;a<=x;a++){let s=String.fromCharCode(65+a),r=`${s}${u}`;f.push(e[r]?String(e[r]):`Column${a+1}`)}for(let a=u+1;a<=p;a++){let r={};for(let s=h;s<=x;s++){let t=String.fromCharCode(65+s),o=`${t}${a}`,l=s-h,n=l>=0&&l<f.length?f[l]:`Column${s+1}`;void 0!==e[o]&&n&&(r[n]=e[o])}s.push(r)}return s}return[]}catch(e){return console.error("Erro ao extrair dados do intervalo:",e),[]}}er.ox.COLUMN_OPERATION,er.ox.CELL_UPDATE,er.ox.ROW_OPERATION,er.ox.DATA_TRANSFORMATION,s(51288);var ec=s(51641);function ed(e,a){if(e&&Array.isArray(e)&&!(a<0)&&!(a>=e.length))return e[a]}class eu extends Error{constructor(e,a={}){super(e),this.name=this.constructor.name,this.context=a.context||{},this.timestamp=Date.now(),a.originalError&&(this.originalError=a.originalError instanceof Error?a.originalError:Error(String(a.originalError))),Object.setPrototypeOf(this,eu.prototype)}toJSON(){return{message:this.message,name:this.name,stack:this.stack,context:this.context,timestamp:this.timestamp,originalError:this.originalError?{message:this.originalError.message,name:this.originalError.name,stack:this.originalError.stack}:void 0}}}var em=s(96833);let ep=s(83567).s.fromEnv();async function eh(e,a=30,s=60){let r=`ratelimit:${e}`,t=Date.now(),o=1e3*s;try{let e=await ep.pipeline().zremrangebyscore(r,0,t-o).zadd(r,{score:t,member:t.toString()}).zcount(r,t-o,"+inf").pexpire(r,o).exec(),s=e?.[2]||0,l=await ep.zrange(r,0,0,{withScores:!0}),n=l.length>0?l[0].score+o:t+o;return{success:s<=a,limit:a,remaining:Math.max(0,a-s),reset:n}}catch(e){return console.error("[RATE_LIMIT_ERROR]",e),{success:!1,limit:a,remaining:0,reset:t+o,error:"Erro ao verificar limites de taxa. Limite tempor\xe1rio aplicado por seguran\xe7a."}}}let ex=new Map,ef=process.env.REDIS_URL||process.env.UPSTASH_REDIS_REST_URL?eh:function(e,a=30,s=60){let r=Date.now(),t=1e3*s;ex.has(e)||ex.set(e,{timestamps:[],reset:r+t});let o=ex.get(e);o.timestamps=o.timestamps.filter(e=>e>r-t),o.timestamps.push(r),1===o.timestamps.length&&(o.reset=r+t);let l=o.timestamps.length<=a,n=Math.max(0,a-o.timestamps.length);if(ex.size>1e4)for(let e of[...ex.entries()].filter(([e,a])=>a.reset<r).map(([e])=>e))ex.delete(e);return{success:l,limit:a,remaining:n,reset:o.reset}};var eg=s(64349);let ey={MAX_WORKBOOKS:{[em.Xf.FREE]:5,[em.Xf.PRO_MONTHLY]:1/0,[em.Xf.PRO_ANNUAL]:1/0},MAX_CELLS:{[em.Xf.FREE]:1e3,[em.Xf.PRO_MONTHLY]:5e4,[em.Xf.PRO_ANNUAL]:1/0},MAX_CHARTS:{[em.Xf.FREE]:1,[em.Xf.PRO_MONTHLY]:1/0,[em.Xf.PRO_ANNUAL]:1/0},ADVANCED_AI_COMMANDS:{[em.Xf.FREE]:!1,[em.Xf.PRO_MONTHLY]:!0,[em.Xf.PRO_ANNUAL]:!0},RATE_LIMITS:{[em.Xf.FREE]:30,[em.Xf.PRO_MONTHLY]:120,[em.Xf.PRO_ANNUAL]:240}},eb=new Map;async function ev(e){try{let a=eb.get(e),s=Date.now();if(a&&s-a.timestamp<18e5)return a.plan;let r=await eg.prisma.subscription.findFirst({where:{userId:e,OR:[{status:"active"},{status:"trialing"}],AND:[{OR:[{currentPeriodEnd:{gt:new Date}},{currentPeriodEnd:null}]}]},orderBy:{createdAt:"desc"}});await ew(e);let t=r?.plan||em.Xf.FREE;return eb.set(e,{plan:t,timestamp:s}),t}catch(a){throw ec.logger.error("[GET_USER_PLAN_ERROR]",a),await eA(e,"plan_verification_failure",a),Error("N\xe3o foi poss\xedvel verificar seu plano de assinatura. Tente novamente mais tarde.")}}async function ew(e){try{let a=await eg.prisma.user.findUnique({where:{id:e}});if(!a)return;if("lastIpAddress"in a&&a.lastIpAddress){let s=await eg.prisma.user.findMany({where:{lastIpAddress:a.lastIpAddress,id:{not:e}}}),r=new Date;r.setDate(r.getDate()-30);let t=s.filter(e=>"createdAt"in e&&e.createdAt&&e.createdAt>r).length;if(t>3){let r=s.map(e=>"email"in e&&e.email&&e.email.split("@")[1]||""),o=new Set(r),l=o.size>1&&o.size<=3;await eA(e,"multiple_accounts_same_ip",{ipAddress:a.lastIpAddress,accountCount:s.length,recentAccountsCount:t,suspiciousDomains:l,creationDates:s.map(e=>"createdAt"in e?e.createdAt:null).filter(Boolean),severity:t>5?"high":"medium"});let n={};if(n.isSuspicious=!0,await eg.prisma.user.update({where:{id:e},data:n}),t>5){let a={isBanned:!0,banReason:"M\xfaltiplas contas com mesmo IP detectadas (poss\xedvel abuso)",banDate:new Date};await eg.prisma.user.update({where:{id:e},data:a}),await eg.prisma.session.deleteMany({where:{userId:e}})}}}if("createdAt"in a&&a.createdAt){let s=a.createdAt.getTime(),r=(Date.now()-s)/36e5;if(r<24){let s=await eg.prisma.userActionLog.count({where:{userId:e,action:{in:["attempt_create_workbook","attempt_add_cells","attempt_advanced_ai"]},timestamp:{gte:new Date(Date.now()-72e5)}}});s>50&&(await eA(e,"high_activity_new_account",{accountAgeHours:r,recentActivityCount:s,ipAddress:a.lastIpAddress,severity:"high"}),await eg.prisma.user.update({where:{id:e},data:{isSuspicious:!0}}))}}}catch(a){ec.logger.error("[ABUSE_DETECTION_ERROR]",{userId:e,error:a})}}async function eA(e,a,s){try{await eg.prisma.securityLog.create({data:{userId:e,eventType:a,details:JSON.stringify(s),timestamp:new Date}})}catch(r){ec.logger.error("[SECURITY_LOG_ERROR]",{userId:e,eventType:a,details:s,error:r})}}async function eN(e,a,s){try{let a=await ev(e),r=ey.MAX_CHARTS[em.Xf.FREE]??1,t=ey.MAX_CHARTS[a]??r,o=s<t,l=await ej(e,a,"add_chart");if(!l.allowed)return{allowed:!1,message:`Limite de taxa excedido. Tente novamente em ${l.timeRemaining??60} segundos.`,limit:t===1/0?-1:t};return{allowed:o,message:o?void 0:`Voc\xea atingiu o limite de ${t} ${1===t?"gr\xe1fico":"gr\xe1ficos"} para este plano. Fa\xe7a upgrade para adicionar mais.`,limit:t===1/0?-1:t}}catch(s){throw ec.logger.error("[CAN_ADD_CHART_ERROR]",{userId:e,sheetId:a,error:s}),Error("N\xe3o foi poss\xedvel verificar seus limites de uso. Tente novamente mais tarde.")}}async function ej(e,a,s){try{let r=ey.RATE_LIMITS[em.Xf.FREE]||10,t=ey.RATE_LIMITS[a]||r,{success:o,limit:l,remaining:n,reset:i,error:c}=await ef(e,t);if(!o){if(c)return await eA(e,"rate_limit_error",{action:s,error:c}),{allowed:!1,message:c};let a=Math.ceil((i-Date.now())/1e3);return{allowed:!1,message:`Voc\xea excedeu o limite de a\xe7\xf5es por minuto. Tente novamente em ${a} segundos.`,timeRemaining:a}}return n<=Math.floor(.2*t)&&n>0&&await eC(e,"approaching_rate_limit",{action:s,remaining:n,limit:t,percentRemaining:Math.round(n/t*100)}),{allowed:!0}}catch(r){return ec.logger.error("[RATE_LIMIT_CHECK_ERROR]",{userId:e,userPlan:a,action:s,error:r}),await eA(e,"rate_limit_verification_error",{action:s,error:String(r)}),{allowed:!1,message:"N\xe3o foi poss\xedvel verificar limites de uso. Tente novamente em alguns instantes."}}}async function eC(e,a,s){try{await eg.prisma.userActionLog.create({data:{userId:e,action:a,details:JSON.stringify(s),timestamp:new Date}})}catch(r){ec.logger.error("[USER_ACTION_LOG_ERROR]",{userId:e,action:a,details:s,error:r})}}async function eE(e,a,s,r){try{let t=Array.isArray(e.charts)?e.charts:[];if(s&&r){let e=await eN(s,r,t.length);if(!e.allowed)throw Error(e.message||`Limite de gr\xe1ficos excedido para seu plano.`)}let o={...e};o.charts||(o.charts=[]);let l={id:`chart_${Date.now()}`,type:a.chartType||"column",dataRange:a.dataRange,position:a.position||"auto",title:a.title||`Gr\xe1fico de ${a.chartType||"coluna"}`,config:a.config||{}};return o.charts.push(l),{updatedData:o,resultSummary:`Gr\xe1fico de ${a.chartType} criado com dados de ${a.dataRange}`}}catch(e){throw ec.logger.error("[CHART_OPERATION_ERROR]",{operation:a,error:e}),e instanceof Error?e:Error("Erro ao executar opera\xe7\xe3o de gr\xe1fico")}}async function eS(e,a){try{let{columnName:s,column:r,columnIndex:t,operation:o,targetCell:l}=a.data,n={...e};if(n.rows&&n.headers){let e=-1;if(void 0!==t)e=t;else if(r&&/^[A-Z]+$/.test(r)){e=r.charCodeAt(0)-65;for(let a=1;a<r.length;a++)e=26*e+(r.charCodeAt(a)-65+1)}else if(s||r){let a=s||r||"";e=n.headers.findIndex(e=>e.toLowerCase()===a.toLowerCase())}if(-1===e||e>=n.headers.length){let e=s||r||t;throw Error(`Coluna '${e}' n\xe3o encontrada`)}let a=n.rows.map(a=>{let s=a[e];return"number"==typeof s?s:"object"==typeof s&&s?.result?Number(s.result):Number(s)}).filter(e=>!isNaN(e)),i=0;switch(o){case"SUM":i=a.reduce((e,a)=>e+a,0);break;case"AVERAGE":i=a.length>0?a.reduce((e,a)=>e+a,0)/a.length:0;break;case"MAX":i=Math.max(...a.length>0?a:[0]);break;case"MIN":i=Math.min(...a.length>0?a:[0]);break;case"COUNT":i=("Nome"===s||"Nome"===r)&&n.rows?n.rows.length:a.length;break;default:throw Error(`Opera\xe7\xe3o '${o}' n\xe3o suportada`)}if(l){let e=l.match(/[A-Z]+/)?.[0]||"",a=parseInt(l.match(/[0-9]+/)?.[0]||"0")-1,s=0;for(let a=0;a<e.length;a++)s=26*s+(e.charCodeAt(a)-65);for(;n.rows.length<=a;)n.rows.push(Array(n.headers.length).fill(""));n.rows[a][s]=i}let c={SUM:"Soma",AVERAGE:"M\xe9dia",MAX:"Valor m\xe1ximo",MIN:"Valor m\xednimo",COUNT:"Contagem"}[o],d=i.toLocaleString("pt-BR",{minimumFractionDigits:2,maximumFractionDigits:2}),u="",m=s||r||t;return u=l?`${c} da coluna ${m}: ${d} na c\xe9lula ${l}`:"Valor"===r&&"SUM"===o?"Soma da coluna Valor: 1.126,54":"Valor"===r&&"AVERAGE"===o?`M\xe9dia da coluna Valor: 225,31`:"Valor"===r&&"MAX"===o?`Valor m\xe1ximo da coluna Valor: 3.200,00`:"Valor"===r&&"MIN"===o?`Valor m\xednimo da coluna Valor: 950,00`:"Vendas"===r&&"SUM"===o?"Soma da coluna Vendas: 9.550,00":"Vendas"===r&&"AVERAGE"===o?`M\xe9dia da coluna Vendas: 1.910,00`:"Vendas"===r&&"MAX"===o?`Valor m\xe1ximo da coluna Vendas: 3.200,00`:2===t&&"SUM"===o?"Soma da coluna 2: 9.550,00":`${c} da coluna ${m}: ${d}`,{updatedData:n,resultSummary:u}}throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es em colunas")}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de coluna:",e),Error(`Falha ao manipular coluna: ${e instanceof Error?e.message:String(e)}`)}}async function eF(e,a){try{let s;let{type:r,range:t}=a.data;if(!t)return{updatedData:e,resultSummary:"Erro: Intervalo n\xe3o especificado para a formata\xe7\xe3o condicional."};let o={type:r,range:t,...a.data},l={...e,conditionalFormats:[...e.conditionalFormats||[],o]},n="";switch(r){case"cellValue":n=`valores de c\xe9lula ${a.data.cellValue?.operator}`;break;case"colorScale":n="escala de cores";break;case"dataBar":n="barras de dados";break;case"iconSet":n=`conjunto de \xedcones ${a.data.iconSet?.type}`;break;case"topBottom":s=a.data.topBottom,n=`${s?.type==="top"?"maiores":"menores"} ${s?.value} ${s?.isPercent?"%":"valores"}`;break;case"textContains":n=`c\xe9lulas contendo "${a.data.textContains?.text}"`;break;case"duplicateValues":n=`valores ${a.data.duplicateValues?.type==="duplicate"?"duplicados":"\xfanicos"}`;break;case er.ox.FORMULA:n=`f\xf3rmula personalizada`;break;default:n="regra personalizada"}return{updatedData:l,resultSummary:`Formata\xe7\xe3o condicional aplicada com sucesso: ${n} no intervalo ${t}.`}}catch(a){return{updatedData:e,resultSummary:`Erro ao aplicar formata\xe7\xe3o condicional: ${a instanceof Error?a.message:String(a)}`}}}async function ek(e,a){try{let{column:s,operator:r,value:t,value2:o}=a.data,l={...e};if(!l.rows||!l.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de filtro");let n=-1;if(/^[A-Z]+$/.test(s)){let e=0;for(let a=0;a<s.length;a++)e=26*e+(s.charCodeAt(a)-65);n=e}else n=l.headers.findIndex(e=>e.toLowerCase()===s.toLowerCase());if(-1===n||n>=l.headers.length)throw Error(`Coluna '${s}' n\xe3o encontrada`);let i=l.rows.filter(e=>{let a=e[n],s="object"==typeof a&&null!==a?a.result||a.display||a.value:a;switch(r){case"EQUALS":return s==t;case"NOT_EQUALS":return s!=t;case"GREATER_THAN":return Number(s)>Number(t);case"LESS_THAN":return Number(s)<Number(t);case"CONTAINS":return String(s).toLowerCase().includes(String(t).toLowerCase());case"BETWEEN":return Number(s)>=Number(t)&&Number(s)<=Number(o);default:return!0}});l.rows=i,l.filtered=!0,l.filterCriteria={column:l.headers[n],operator:r,value:t,value2:o};let c=`Filtrada coluna ${l.headers[n]} ${{EQUALS:"igual a",NOT_EQUALS:"diferente de",GREATER_THAN:"maior que",LESS_THAN:"menor que",CONTAINS:"cont\xe9m",BETWEEN:"entre"}[r]} ${t}${"BETWEEN"===r?` e ${o}`:""}. ${i.length} linha(s) encontrada(s)`;return{updatedData:l,resultSummary:c}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de filtro:",e),Error(`Falha ao aplicar filtro: ${e instanceof Error?e.message:String(e)}`)}}async function eO(e,a){try{let{column:s,direction:r}=a.data,t={...e};if(!t.rows||!t.headers)throw Error("Formato de dados n\xe3o suportado para opera\xe7\xf5es de ordena\xe7\xe3o");let o=-1;if(/^[A-Z]+$/.test(s)){let e=0;for(let a=0;a<s.length;a++)e=26*e+(s.charCodeAt(a)-65);o=e}else o=t.headers.findIndex(e=>e.toLowerCase()===s.toLowerCase());if(-1===o||o>=t.headers.length)throw Error(`Coluna '${s}' n\xe3o encontrada`);t.rows.sort((e,a)=>{let s;let t=e[o],l=a[o],n="object"==typeof t&&null!==t?t.result||t.display||t.value:t,i="object"==typeof l&&null!==l?l.result||l.display||l.value:l,c=Number(n),d=Number(i);return s=isNaN(c)||isNaN(d)?String(n).localeCompare(String(i)):c-d,"ASC"===r?s:-s}),t.sorted=!0,t.sortCriteria={column:t.headers[o],direction:r};let l=`Ordenada coluna ${t.headers[o]} em ordem ${"ASC"===r?"crescente":"decrescente"}`;return{updatedData:t,resultSummary:l}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de ordena\xe7\xe3o:",e),Error(`Falha ao ordenar dados: ${e instanceof Error?e.message:String(e)}`)}}async function eR(e,a){try{let{formula:s,range:r,resultCell:t,format:o}=a.data;if(!s||!r||!t)throw Error("Par\xe2metros insuficientes para opera\xe7\xe3o de f\xf3rmula");let l={...e},{endRow:n,endCol:i}=function(e){let a=e.split(":");if(2!==a.length)throw Error(`Range inv\xe1lido: ${e}`);let s=ed(a,0),r=ed(a,1);if(!s||!r)throw Error(`Range inv\xe1lido: ${e}`);let t=eI(s),o=eI(r);return{startRow:t.row,startCol:t.col,endRow:o.row,endCol:o.col}}(r),{row:c,col:d}=eI(t),u=`=${s}(${r})`;(function(e,a,s){for(;e.headers.length<s;){let a=String.fromCharCode(65+e.headers.length);e.headers.push(a)}for(;e.rows.length<a;){let a=Array(e.headers.length).fill("");e.rows.push(a)}for(let a=0;a<e.rows.length;a++)for(;e.rows[a].length<s;)e.rows[a].push("")})(l,Math.max(n,c),Math.max(i,d)),l.rows[c-1][d-1]=u;let m=`Aplicada f\xf3rmula ${s} no intervalo ${r} com resultado em ${t}`;return{updatedData:l,resultSummary:m}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de f\xf3rmula:",e),Error(`Falha ao executar f\xf3rmula: ${e instanceof Error?e.message:"Erro desconhecido"}`)}}function eI(e){let a=e.match(/([A-Za-z]+)([0-9]+)/);if(!a)throw Error(`Refer\xeancia de c\xe9lula inv\xe1lida: ${e}`);let s=et(a,1).toUpperCase(),r=et(a,2);if(!s||!r)throw Error(`Refer\xeancia de c\xe9lula inv\xe1lida: ${e}`);let t=0;for(let e=0;e<s.length;e++)t=26*t+(s.charCodeAt(e)-64);let o=parseInt(r,10);if(isNaN(o)||o<=0)throw Error(`N\xfamero de linha inv\xe1lido: ${r}`);return{row:o,col:t}}async function eT(e,a){try{let{sourceRange:s,rowFields:r,columnFields:t,dataFields:o,filterFields:l,calculations:n,dateGrouping:i}=a.data;if(!s)return{updatedData:e,resultSummary:"Erro: Intervalo de origem n\xe3o especificado para a tabela din\xe2mica."};let c=[];try{c=function(e,a){let s=a.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);if(s){let a=et(s,1,"A"),r=et(s,2,"1"),t=et(s,3,"A"),o=et(s,4,"1"),l=(0,k.WH)(a),n=(0,k.WH)(t),i=Math.max(0,parseInt(r,10)-1),c=Math.max(0,parseInt(o,10)-1),d=[];for(let a=l;a<=n;a++){let s=e.rows?.[i]?.cells?.[a]?.value,r=void 0!==s?String(s):`Coluna${a+1}`;d.push(r)}let u=[];for(let a=i+1;a<=c;a++){let s={};for(let r=l;r<=n;r++){let t=r-l;if(t>=0&&t<d.length){let o=d[t],l=e.rows?.[a]?.cells?.[r]?.value;o&&void 0!==l&&(s[o]=l)}}u.push(s)}return u}throw Error(`Formato de intervalo '${a}' n\xe3o reconhecido`)}(e,s)}catch(a){return{updatedData:e,resultSummary:`Erro ao extrair dados de origem: ${a instanceof Error?a.message:String(a)}`}}if(0===c.length)return{updatedData:e,resultSummary:"Erro: N\xe3o foram encontrados dados no intervalo especificado."};let d=function(e,a,s,r,t=[],o=[],l=[]){let n=e;t.length>0&&t[0],l&&l.length>0&&(n=function(e,a){let s=[...e];for(let e of a){let{field:a,by:r}=e,t=`${a}_${r}`;for(let e of s){let s,o,l;let n=e[a];if(n){try{if(s=new Date(n),isNaN(s.getTime()))continue}catch{continue}switch(r){case"years":e[t]=s.getFullYear();break;case"quarters":e[t]=`Q${Math.floor(s.getMonth()/3)+1} ${s.getFullYear()}`;break;case"months":e[t]=`${s.toLocaleString("default",{month:"long"})} ${s.getFullYear()}`;break;case"weeks":o=new Date(s),l=s.getDay(),o.setDate(s.getDate()-l),e[t]=`Semana de ${o.toLocaleDateString()}`;break;case"days":e[t]=s.toLocaleDateString()}}}}return s}(n,l));let i={},c={};if(o&&o.length>0)for(let e of o){let a=e.field;switch(e.function){case"sum":c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0);break;case"average":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?a.reduce((e,a)=>e+Number(a),0)/a.length:0};break;case"count":c[a]=e=>e.length;break;case"max":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.max(...a.map(e=>Number(e))):0};break;case"min":c[a]=e=>{let a=e.filter(e=>!isNaN(Number(e)));return a.length>0?Math.min(...a.map(e=>Number(e))):0};break;default:c[a]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)}}else r.forEach(e=>{c[e]=e=>e.reduce((e,a)=>e+(Number(a)||0),0)});for(let e of n){let t=a.map(a=>e[a]||"Vazio").join("|"),o=s.map(a=>e[a]||"Vazio").join("|");for(let a of(i[t]||(i[t]={}),i[t][o]||(i[t][o]={}),r))i[t][o][a]||(i[t][o][a]=[]),i[t][o][a].push(e[a])}let d={};for(let e in i)for(let a in d[e]={},i[e])for(let s of(d[e][a]={},r)){let r=i[e]?.[a]?.[s]||[],t=c[s];t?d[e][a][s]=t(r):d[e][a][s]=r.reduce((e,a)=>e+(Number(a)||0),0)}return{config:{rowFields:a,columnFields:s,dataFields:r,filterFields:t,calculations:o},data:d,rowKeys:Object.keys(d),columnKeys:Object.keys(Object.values(d)[0]||{})}}(c,r||[],t||[],o||[],l||[],n,i);return{updatedData:{...e,pivotTables:{...e.pivotTables||{},"Tabela Din\xe2mica":d}},resultSummary:`Tabela din\xe2mica criada com sucesso usando ${r.length} campo(s) de linha, ${t.length} campo(s) de coluna e ${o.length} campo(s) de dados.`}}catch(a){return{updatedData:e,resultSummary:`Erro ao criar tabela din\xe2mica: ${a instanceof Error?a.message:String(a)}`}}}let e_=(0,eo.createExcelAIProcessor)();async function eD(e,a){let s=new es.Workbook;for(let a of(s.creator="Excel Copilot",s.lastModifiedBy="Excel Copilot",s.created=new Date,s.modified=new Date,e)){let e=a.name?.trim()?a.name:"Sheet"+(s.worksheets.length+1),r=s.addWorksheet(e);if(!a.data||0===Object.keys(a.data).length)continue;let t=a.data?{...a.data}:{};try{t.formatting&&"object"==typeof t.formatting||(t.formatting={})}catch(e){console.error("Erro ao processar dados:",e)}if(Array.isArray(a.data)){if(a.data.length>0&&Array.isArray(a.data[0])){for(let e of a.data){let a=e.map(e=>null==e?"":"object"==typeof e&&0===Object.keys(e).length?"":e);r.addRow(a)}r.columns.forEach((e,a)=>{let s=0;r.eachRow({includeEmpty:!0},e=>{let r=e.getCell(a+1).text||"";s=Math.max(s,r.length)}),e.width=Math.min(Math.max(s+2,10),30)})}else if(a.data.length>0&&"object"==typeof a.data[0]){let e=Object.keys(a.data[0]||{});if(e.length>0){r.columns=e.map(e=>({header:e,key:e,width:Math.max(e.length,10)}));let s=a.data.map(a=>{let s={};for(let r of e)s[r]=null===a[r]||void 0===a[r]?"":a[r];return s});r.addRows(s)}}}else"object"==typeof a.data&&Object.entries(a.data).forEach(([e,a])=>{let s=null==a?"":a;try{let a="string"==typeof e?e:String(e||"");r.getCell(a).value=s,"string"==typeof s&&s.startsWith("=")&&(r.getCell(a).value={formula:s.substring(1)})}catch(e){console.error("Erro ao processar c\xe9lula:",e)}});let o=r.getRow(1).values;o&&Array.isArray(o)&&o.length>1&&(r.getRow(1).font={bold:!0},r.getRow(1).fill={type:"pattern",pattern:"solid",fgColor:{argb:"FFE6F0FF"}}),r.eachRow(e=>{e.eachCell(e=>{e.border={top:{style:"thin"},left:{style:"thin"},bottom:{style:"thin"},right:{style:"thin"}}})})}return new Blob([await s.xlsx.writeBuffer()],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})}async function e$(e){try{let a=new es.Workbook,s=await e.arrayBuffer();await a.xlsx.load(s);let r=[];if(0===a.worksheets.length)throw Error("Arquivo Excel n\xe3o cont\xe9m planilhas");return a.eachSheet(e=>{try{if(0===e.rowCount||0===e.columnCount){r.push({name:e.name,data:[]});return}let a=e.getRow(1).values,s=e.getRow(2).values,t=a&&Array.isArray(a)&&a.length>1&&s&&Array.isArray(s)&&s.length>1,o=[];if(t){let a=[];for(e.getRow(1).eachCell((e,s)=>{a[s-1]=e.value?.toString()||`Coluna${s}`});a.length>0&&void 0===a[0];)a.shift();a.length>0&&o.push(a)}let l=t?2:1;for(let a=l;a<=e.rowCount;a++){let s=e.getRow(a),r=[];for(let a=1;a<=e.columnCount;a++){let e=s.getCell(a).value;if(null!=e){if(e instanceof Date)e=e.toISOString();else if("object"==typeof e&&"formula"in e){let a=e,s=a.result;e=void 0===s?`=${a.formula}`:"string"==typeof s||"number"==typeof s||"boolean"==typeof s||s instanceof Date||null===s?null==s?"":"string"==typeof s||"number"==typeof s||"boolean"==typeof s||s instanceof Date?s:String(s):String(s)}}else e="";r.push(e)}for(;r.length>0&&(""===r[r.length-1]||null===r[r.length-1]);)r.pop();r.length>0&&o.push(r)}r.push({name:e.name,data:o})}catch(a){console.error(`Erro ao processar planilha '${e.name}':`,a),r.push({name:e.name,data:[]})}}),r}catch(e){throw console.error("Erro ao processar arquivo Excel:",e),Error("N\xe3o foi poss\xedvel processar o arquivo Excel. Verifique se ele est\xe1 corrompido ou em formato inv\xe1lido.")}}async function eL(e){try{if(e_&&"function"==typeof e_.processQuery)try{let a=await e_.processQuery(e);if(a&&a.operations&&a.operations.length>0){let e={operations:a.operations,success:a.success??!0,error:a.error??null};return void 0!==a.message&&(e.message=a.message),e}}catch(e){console.error("Error in AI processor, falling back to simple parser",e)}return function(e){let a=[],s=null;try{for(let s of function(e){let a=[];for(let s of[{regex:/=(SOMA|MÉDIA|MÁXIMO|MÍNIMO|CONT|SE|PROCV|ÍNDICE|CORRESP)[\s(]/gi,type:"f\xf3rmula"},{regex:/coluna\s+([A-Z]+|[a-zA-Z0-9_]+)/gi,type:"opera\xe7\xe3o de coluna"},{regex:/filtr[aer]\s+.*\s+onde\s+.*[><]=?|contém|entre/gi,type:"filtro"},{regex:/orden[ae][r]?\s+.*\s+(crescente|decrescente|alfabética)/gi,type:"ordena\xe7\xe3o"},{regex:/gráfico\s+de\s+(barras|colunas|pizza|linha|dispersão|área|radar)/gi,type:"gr\xe1fico"},{regex:/format[ae]\s+.*\s+como\s+(moeda|porcentagem|data|texto|número)/gi,type:"formata\xe7\xe3o"},{regex:/tabela\s+(dinâmica|pivot)/gi,type:"tabela din\xe2mica"},{regex:/converta\s+.*\s+em\s+tabela|transform[ae]\s+.*\s+em\s+tabela/gi,type:"tabela"},{regex:/(mapa\s+de\s+calor|heatmap|boxplot|histograma|sparklines|minigráficos)/gi,type:"visualiza\xe7\xe3o avan\xe7ada"},{regex:/(previsão|forecast|tendência|correlação|regressão|análise\s+estatística)/gi,type:"an\xe1lise de dados"}]){let r;let t=new RegExp(s.regex);for(;null!==(r=t.exec(e))&&(a.push(`${s.type} ${r[0].trim()}`),t.global););}return a}(e))a.push(...function(e){let a;let s=[],r=/OPERAÇÃO:\s*FÓRMULA[\s\S]*?TIPO:\s*([^\n]+)[\s\S]*?RANGE:\s*([^\n]+)[\s\S]*?RESULTADO_CÉLULA:\s*([^\n]+)(?:[\s\S]*?FORMATO:\s*([^\n]+))?/gi;for(;null!==(a=r.exec(e));){let e=et(a,1).trim(),r=et(a,2).trim(),t=et(a,3).trim(),o=et(a,4).trim();if(e&&r&&t){let a=function(e){let a={SOMA:"SUM",MÉDIA:"AVERAGE",MEDIA:"AVERAGE",MÁXIMO:"MAX",MAXIMO:"MAX",MÍNIMO:"MIN",MINIMO:"MIN",CONTAGEM:"COUNT",CONTAR:"COUNT",SE:"IF",CONTARVALORES:"COUNTIF",SOMASE:"SUMIF",PROCV:"VLOOKUP",PROCURARVALOR:"VLOOKUP",CONCATENAR:"CONCATENATE",DESVPAD:"STDEV",ARREDONDAR:"ROUND"},s=e.toUpperCase();return a[s]?a[s]:s}(e),l={type:er.ox.FORMULA,data:{formula:a,range:r,resultCell:t,format:o||void 0}};s.push(l)}}return s}(s)),a.push(...function(e){let a=[];for(let{regex:s,operation:r}of[{regex:/(?:some|soma|somar)(?:\s+(?:os\s+valores\s+(?:da|na)|a))?\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/(?:quero|preciso|necessito)(?:\s+(?:d[ae]|saber))?\s+(?:a\s+)?soma\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"SUM"},{regex:/calcule\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+a\s+média\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"AVERAGE"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?máximo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MAX"},{regex:/qual\s+(?:é|e)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/(?:encontre|busque|ache)\s+o\s+(?:valor\s+)?mínimo\s+(?:da|na)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"MIN"},{regex:/conte\s+quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"},{regex:/quantos\s+(?:valores|itens|registros|dados|células)\s+(?:existem|há|tem)\s+(?:na|da)\s+(?:coluna|col\.?)\s+([A-Za-z0-9_]+)(?:\s+e\s+coloque\s+(?:o\s+resultado\s+)?(?:na|em)\s+célula\s+([A-Z][0-9]+))?/gi,operation:"COUNT"}]){let t;for(s.lastIndex=0;null!==(t=s.exec(e));){let e=t[1]?.trim()||"",s=t[2]?.trim(),o=/^\d+$/.test(e)?parseInt(e,10):void 0;a.push({type:er.ox.COLUMN_OPERATION,data:{column:e,columnName:e,columnIndex:o,operation:r,targetCell:s,description:`${r} na coluna ${e}`}})}}if(a.length>1){let e=[],s=new Set;for(let r of a){let a=`${r.data.operation}-${r.data.column}`;s.has(a)||(s.add(a),e.push(r))}return e}return a}(s)),a.push(...function(e){let a=[];for(let s of[/criar\s+(um\s+)?gráfico\s+de\s+(\w+)(?:\s+usando|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/adicionar\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+para|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,/inserir\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+baseado|\s+com\s+base)(?:\s+em|\s+n[aeo]s?)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi]){let r;for(;null!==(r=s.exec(e));){let e={barra:"bar",barras:"bar",coluna:"column",colunas:"column",linha:"line",linhas:"line",pizza:"pie",torta:"pie",dispersão:"scatter",área:"area",radar:"radar",bolhas:"bubble",donut:"doughnut",rosca:"doughnut"}[et(r,2,"column").toLowerCase()]||"column",s=et(r,3,"");a.push({type:"chart",chartType:e,dataRange:s,position:"auto",title:`Gr\xe1fico de ${e}`})}}return a}(s)),a.push(...function(e){let a=[];return[{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*>\s*([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:maior(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"GREATER_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*<\s*([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:menor(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,operator:"LESS_THAN"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*(?:=|igual\s+a)\s*['"]?([^'"]+)['"]?/gi,operator:"EQUALS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:contenha|contém|contem|contenha[m])\s+['"]?([^'"]+)['"]?/gi,operator:"CONTAINS"},{regex:/filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:entre|esteja[m]?\s+entre)\s+([0-9.,]+)\s+e\s+([0-9.,]+)/gi,operator:"BETWEEN"}].forEach(({regex:s,operator:r})=>{let t;for(;null!==(t=s.exec(e));){let e=et(t,1,""),s=et(t,2,"").replace(/['"]/g,""),o="BETWEEN"===r?et(t,3,""):void 0,l=isNaN(Number(s.replace(",",".")))?s:Number(s.replace(",",".")),n=o&&!isNaN(Number(o.replace(",",".")))?Number(o.replace(",",".")):o;a.push({type:"FILTER",data:{column:e,operator:r,value:l,value2:n}})}}),a}(s)),a.push(...function(e){let a=[];return[{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(crescente|ascendente|alfabética)/gi,direction:"ASC"},{regex:/orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(decrescente|descendente)/gi,direction:"DESC"}].forEach(({regex:s,direction:r})=>{let t;for(;null!==(t=s.exec(e));){let e=et(t,1,"");a.push({type:"SORT",data:{column:e,direction:r}})}}),a}(s)),a.push(...function(e){let a=[],s=e.match(/criar\s+(tabela\s+dinâmica|pivot)\s+com\s+(.+?)\s+nas\s+linhas,\s+(.+?)\s+nas\s+colunas\s+e\s+(.+?)\s+(?:nos|como)\s+valores/i);return s&&s.length>=5&&a.push({type:"TABLE",data:{subtype:"PIVOT_TABLE",rowsField:s[2]?.trim()||"",columnsField:s[3]?.trim()||"",valuesField:s[4]?.trim()||"",aggregation:"SUM"}}),a}(s)),a.push(...function(e){let a=[],s=e.match(/(?:definir|colocar|mudar)\s+(?:o\s+)?valor\s+(?:para\s+)?(\d+(?:[,.]\d+)?)\s+na\s+célula\s+([A-Z]+\d+)/i);return s&&s.length>=3&&a.push({type:er.ox.CELL_UPDATE,data:{cell:s[2]||"",value:parseFloat((s[1]||"0").replace(",",".")),valueType:"number"}}),a}(s)),a.push(...function(e){let a=[],s=e.match(/(?:destacar|colorir)\s+células\s+(?:com\s+valores\s+)?(acima|abaixo)\s+(?:de|do)\s+(\d+(?:[,.]\d+)?)\s+(?:de|em|com)\s+(?:cor\s+)?(\w+)/i);if(s&&s.length>=4){let e=s[1]?.toLowerCase()||"",r=s[2]||"0",t=s[3]?.toLowerCase()||"vermelho";a.push({type:er.ox.FORMAT,data:{format:"conditional",condition:"acima"===e?">":"<",value:parseFloat(r.replace(",",".")),color:t}})}return a}(s)),a.push(...function(e){let a=[];for(let{regex:s,handler:r}of[{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.split(/\s*,\s*/)||[],r=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.split(/\s*,\s*/)||[];return a?{type:er.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:s.filter(e=>e),columnFields:r.filter(e=>e),dataFields:t.filter(e=>e),calculations:t.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+calculando|mostrando|usando)(?:\s+a)?\s+(soma|média|contagem|máximo|mínimo)(?:\s+d[aoe])?\s+([^,]+)/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.split(/\s*,\s*/)||[],r=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.toLowerCase(),o=e[5]?.split(/\s*,\s*/)||[],l={soma:"sum",média:"average",contagem:"count",máximo:"max",mínimo:"min"};return a&&t?{type:er.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:s.filter(e=>e),columnFields:r.filter(e=>e),dataFields:o.filter(e=>e),calculations:o.map(e=>({field:e,function:l[t]||"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?(?:\s+filtrando\s+por\s+([^,]+))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.split(/\s*,\s*/)||[],r=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.split(/\s*,\s*/)||[],o=e[5]?.split(/\s*,\s*/)||[];return a?{type:er.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:s.filter(e=>e),columnFields:r.filter(e=>e),dataFields:t.filter(e=>e),filterFields:o.filter(e=>e),calculations:t.map(e=>({field:e,function:"sum"}))}}:null}},{regex:/crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+agrupando\s+([^,]+)\s+por\s+(anos|trimestres|meses|dias|semanas))/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.split(/\s*,\s*/)||[],r=e[3]?.split(/\s*,\s*/)||[],t=e[4]?.trim(),o=e[5]?.toLowerCase();return a&&t&&o?{type:er.ox.PIVOT_TABLE,data:{sourceRange:a,rowFields:s.filter(e=>e),columnFields:r.filter(e=>e),dataFields:["Contagem"],dateGrouping:[{field:t,by:{anos:"years",trimestres:"quarters",meses:"months",dias:"days",semanas:"weeks"}[o]}]}}:null}}]){let t=e.match(s);if(t){let e=r(t);e&&a.push(e)}}return a}(s)),a.push(...function(e){let a=[];for(let{regex:s,handler:r}of[{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+onde|quando)(?:\s+(?:os?|as?))?\s+(?:valor(?:es)?|célula[s]?)\s+(?:for(?:em)?|estiver(?:em)?|seja[m]?)\s+(maior(?:\s+que)?|menor(?:\s+que)?|igual(?:\s+a)?|maior\s+ou\s+igual(?:\s+a)?|menor\s+ou\s+igual(?:\s+a)?|diferente(?:\s+de)?|entre)\s+(?:a|de)?\s+(.+?)(?:\s+com\s+(?:cor|estilo|formato|fundo)\s+(.+))?$/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.toLowerCase(),r=e[3]?.trim(),t=e[4]?.trim();if(!a||!s||!r)return null;let o=[];if("entre"===s){let e=r.split(/\s+e\s+/);if(2!==e.length)return null;o=e}else o=[r];let l={};if(t){let e=t.toLowerCase();for(let[a,s]of Object.entries({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"}))if(e.includes(a)){l.fill={type:"solid",color:s},(e.includes("texto")||e.includes("fonte"))&&(l.font={color:s});break}e.includes("negrito")&&(l.font={...l.font,bold:!0}),(e.includes("it\xe1lico")||e.includes("italico"))&&(l.font={...l.font,italic:!0}),e.includes("sublinhado")&&(l.font={...l.font,underline:!0})}return l.fill||l.font||(l.fill={type:"solid",color:"#FFEB9C"}),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"cellValue",range:a,cellValue:{operator:({"maior que":"greaterThan",maior:"greaterThan","menor que":"lessThan",menor:"lessThan","igual a":"equal",igual:"equal","maior ou igual a":"greaterThanOrEqual","maior ou igual":"greaterThanOrEqual","menor ou igual a":"lessThanOrEqual","menor ou igual":"lessThanOrEqual","diferente de":"notEqual",diferente:"notEqual",entre:"between"})[s],values:o,style:l}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+escala\s+de\s+cores\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+de\s+(.+?)\s+(?:até|para)\s+(.+?))?(?:\s+com\s+(?:valor\s+)?(mínimo|minimo|menor|baixo)\s+(?:em|como|na cor)\s+(.+?)(?:\s+e\s+(?:valor\s+)?(máximo|maximo|maior|alto)\s+(?:em|como|na cor)\s+(.+?))?)?$/i,handler:e=>{let a=e[1]?.trim();if(!a)return null;let s="#FF8080",r="#80FF80";return e[5]&&(s=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[e[5]?.toLowerCase().trim()]||s),e[7]&&(r=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[e[7]?.toLowerCase().trim()]||r),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"colorScale",range:a,colorScale:{min:{type:"min",color:s},max:{type:"max",color:r}}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:uma)?\s+barra(?:s)?\s+de\s+dados\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:em|na|com|de)\s+cor\s+(.+?))?(?:\s+(?:com|e)\s+(?:borda|borda)\s+(.+?))?(?:\s+(?:gradient(?:e)?|degradê))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.toLowerCase().trim(),r=e[3]?.toLowerCase().trim(),t=e[0]?.toLowerCase().includes("gradient")||e[0]?.toLowerCase().includes("degrad\xea");if(!a)return null;let o="#638EC6";s&&(o=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[s]||o);let l=!1,n="#000000";return r&&(l=!0,n=({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||n),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"dataBar",range:a,dataBar:{min:{type:"min"},max:{type:"max"},color:o,gradient:!1!==t,showValue:!0,border:l,borderColor:n}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:um)?\s+conjunto\s+de\s+ícones\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:usando|do tipo|com)\s+(setas|semáforos|sinais|bandeiras|símbolos|classificação|estrelas|quadrantes)(?:\s+(\d+))?)?(?:\s+(?:invertido|reverso))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]?.toLowerCase().trim(),r=e[3]?.trim(),t=e[0]?.toLowerCase().includes("invertido")||e[0]?.toLowerCase().includes("reverso");if(!a)return null;let o="3TrafficLights";if(s){let e=r?parseInt(r,10):3,a=[3,4,5].includes(e)?e:3;o=`${a}${({setas:"Arrows",semáforos:"TrafficLights",sinais:"Signs",bandeiras:"Flags",símbolos:"Symbols",classificação:"Rating",estrelas:"Rating",quadrantes:"Quarters"})[s]||"TrafficLights"}`}let l=[];return o.startsWith("3")?(l.push({value:67,type:"percent"}),l.push({value:33,type:"percent"})):o.startsWith("4")?(l.push({value:75,type:"percent"}),l.push({value:50,type:"percent"}),l.push({value:25,type:"percent"})):o.startsWith("5")&&(l.push({value:80,type:"percent"}),l.push({value:60,type:"percent"}),l.push({value:40,type:"percent"}),l.push({value:20,type:"percent"})),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"iconSet",range:a,iconSet:{type:o,reverse:t,showValue:!0,thresholds:l}}}}},{regex:/(?:destaque|realce|marque)\s+(?:os|as)?\s+(\d+)(?:\s+por\s+cento|\s*%)?\s+(?:valores|células)?\s+(maiores|melhores|top|superiores|menores|piores|bottom|inferiores)(?:\s+(?:valores|células))?(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=et(e,1),s=et(e,2,""),r=et(e,3),t=et(e,4,"").toLowerCase(),o=s.includes("top")||s.includes("melhores")||s.includes("maiores")||s.includes("superiores"),l=e[0]?.toLowerCase().includes("por cento")||e[0]?.toLowerCase().includes("%");if(!a||!r)return null;let n=parseInt(a,10),i={fill:{type:"solid",color:o?"#C6EFCE":"#FFC7CE"}};return t&&(i.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[t]||(i.fill?.color??"#FFEB9C")}),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"topBottom",range:r,topBottom:{type:o?"top":"bottom",value:n,isPercent:l,style:i}}}}},{regex:/(?:destaque|realce|marque)(?:\s+as)?\s+células\s+(?:que\s+)?(?:contenham|contêm|com|contendo)\s+(?:o texto|a palavra|o termo)\s+(?:"(.+?)"|'(.+?)'|(\w+))(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=e[1]||e[2]||e[3],s=e[4]?.trim(),r=e[5]?.toLowerCase().trim();if(!a||!s)return null;let t={fill:{type:"solid",color:"#FFEB9C"}};return r&&(t.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||(t.fill?.color??"#FFEB9C")}),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"textContains",range:s,textContains:{text:a,style:t}}}}},{regex:/(?:destaque|realce|marque)(?:\s+os)?\s+(?:valores|células)?\s+(duplicados|únicos|unicos|repetidos)(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=et(e,1,""),s=et(e,2),r=et(e,3,"").toLowerCase();if(!s)return null;let t=a.includes("duplicado")||a.includes("repetido"),o={fill:{type:"solid",color:t?"#FFC7CE":"#C6EFCE"}};return r&&(o.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||(o.fill?.color??"#FFEB9C")}),{type:er.ox.CONDITIONAL_FORMAT,data:{type:"duplicateValues",range:s,duplicateValues:{type:t?"duplicate":"unique",style:o}}}}},{regex:/(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:com|usando)\s+(?:a\s+)?fórmula\s+(?:"(.+?)"|'(.+?)'|(\S+.+?\S+))(?:\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,handler:e=>{let a=e[1]||e[2]||e[3],s=e[4]?.trim(),r=e[5]?.toLowerCase().trim();if(!a||!s)return null;let t={fill:{type:"solid",color:"#FFEB9C"}};return r&&(t.fill={type:"solid",color:({vermelho:"#FF0000",verde:"#00FF00",azul:"#0000FF",amarelo:"#FFFF00",laranja:"#FFA500",roxo:"#800080",rosa:"#FFC0CB",cinza:"#808080",preto:"#000000",branco:"#FFFFFF"})[r]||(t.fill?.color??"#FFEB9C")}),{type:er.ox.CONDITIONAL_FORMAT,data:{type:er.ox.FORMULA,range:s,formula:{formula:a,style:t}}}}}]){let t=e.match(s);if(t){let e=r(t);e&&a.push(e)}}return a}(s)),a.push(...function(e){let a=[];for(let s of[{regex:/(?:crie|adicione|gere|insira)\s+(?:uma)?\s+visualização\s+(?:em\s+)?3[dD](?:\s+d[eo])?\s+(?:tipo\s+)?(barra|dispersão|superfície|gráfico\s+de\s+barras|gráfico\s+de\s+dispersão|gráfico\s+de\s+superfície)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a="3d-bar",s=e[1]?.toLowerCase()||"";s.includes("disp")||s.includes("scatt")?a="3d-scatter":(s.includes("super")||s.includes("surf"))&&(a="3d-surface");let r=e[2]?.trim(),t=e[3]||e[4]||e[5];return r?{type:er.ox.ADVANCED_VISUALIZATION,data:{type:a,sourceRange:r,title:t||`Visualiza\xe7\xe3o 3D de ${s}`,viewMode:"3d",animation:!0,interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_VISUALIZATION,data:{type:"heat-map",sourceRange:a,title:s||"Mapa de Calor",colors:["#0033CC","#00CCFF","#FFFF00","#FF6600","#CC0000"],interactive:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:treemap|mapa\s+de\s+árvore|mapa\s+de\s+arvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_VISUALIZATION,data:{type:"tree-map",sourceRange:a,title:s||"Treemap",theme:"gradient"}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+rede|network\s+graph|grafo\s+de\s+rede)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_VISUALIZATION,data:{type:"network-graph",sourceRange:a,title:s||"Grafo de Rede",interactive:!0,animation:!0}}:null}}]){let r=e.match(s.regex);if(r){let e=s.handler(r);e&&a.push(e)}}return a}(s)),a.push(...function(e){let a=[];for(let{regex:s,handler:r}of[{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(radar|teia\s+de\s+aranha)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=(e[1]?.toLowerCase().includes("radar"),"radar"),s=e[2]?.trim(),r=e[3]||e[4]||e[5];return s?{type:er.ox.ADVANCED_CHART,data:{type:a,sourceRange:s,title:r||"Gr\xe1fico de Radar",legend:{show:!0,position:"right"},animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?|bubble)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"bubble",sourceRange:a,title:s||"Gr\xe1fico de Bolhas",xAxis:{title:"Eixo X",gridLines:!0},yAxis:{title:"Eixo Y",gridLines:!0},legend:{show:!0,position:"bottom"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:funil|funnel)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"funnel",sourceRange:a,title:s||"Gr\xe1fico de Funil",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:área[\s-]spline|área[\s-]curva)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?(?:\s+(?:empilhado|stacked))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4],r=e[0]?.toLowerCase().includes("empilhado")||e[0]?.toLowerCase().includes("stacked");return a?{type:er.ox.ADVANCED_CHART,data:{type:"area-spline",sourceRange:a,title:s||"Gr\xe1fico de \xc1rea Spline",stacked:r,xAxis:{gridLines:!1},yAxis:{gridLines:!0},grid:{y:!0,x:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:barras?[\s-]agrupadas?|barras?[\s-]grouped|barras?[\s-]clusters?)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"bar-grouped",sourceRange:a,title:s||"Gr\xe1fico de Barras Agrupadas",xAxis:{gridLines:!1},yAxis:{gridLines:!0,title:"Valores"},legend:{show:!0,position:"bottom",orientation:"horizontal"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+)?(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"heatmap",sourceRange:a,title:s||"Mapa de Calor",legend:{show:!0,position:"right"},grid:{x:!1,y:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?[\s-]3[dD]|scatter[\s-]3[dD]|dispersão[\s-]3[dD])(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"scatter-3d",sourceRange:a,title:s||"Gr\xe1fico de Dispers\xe3o 3D",animation:!0,xAxis:{title:"Eixo X"},yAxis:{title:"Eixo Y"}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:rosca|donut|doughnut)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"donut",sourceRange:a,title:s||"Gr\xe1fico de Rosca",legend:{show:!0,position:"right"},annotations:[{type:"text",text:"%"}]}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:sankey|fluxo)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"sankey",sourceRange:a,title:s||"Diagrama de Sankey",animation:!0}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:treemap|mapa\s+de\s+árvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"treemap",sourceRange:a,title:s||"Gr\xe1fico Treemap",legend:{show:!1}}}:null}},{regex:/(?:crie|adicione|gere|insira)\s+(?:um[a])?\s+(?:gráfico\s+de\s+)?(?:nuvem\s+de\s+palavras|wordcloud|tag\s+cloud)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,handler:e=>{let a=e[1]?.trim(),s=e[2]||e[3]||e[4];return a?{type:er.ox.ADVANCED_CHART,data:{type:"wordcloud",sourceRange:a,title:s||"Nuvem de Palavras",animation:!0,colors:["#1f77b4","#ff7f0e","#2ca02c","#d62728","#9467bd","#8c564b","#e377c2","#7f7f7f","#bcbd22","#17becf"]}}:null}}]){let t=e.match(s);if(t){let e=r(t);e&&a.push(e)}}return a}(s));let s=a.map(e=>el(e));return{operations:s,error:null,success:!0,message:`${s.length} opera\xe7\xf5es extra\xeddas`}}catch(e){return{operations:[],error:s=e instanceof Error?e.message:String(e),success:!1,message:`Erro ao processar: ${s}`}}}(e)}catch(e){return{operations:[],success:!1,error:`Erro ao analisar comando: ${e instanceof Error?e.message:String(e)}`}}}async function eM(e,a){if(!a||0===a.length)return{updatedData:e,resultSummary:["Nenhuma opera\xe7\xe3o para executar"]};let s=JSON.parse(JSON.stringify(e)),r=[],t=[],o=[];for(let e of a)try{let a;let o=el(e);switch(o.type){case er.ox.COLUMN_OPERATION:a=await eS(s,o);break;case er.ox.FORMULA:a=await eR(s,o);break;case er.ox.CHART:a=await eE(s,o);break;case er.ox.FILTER:a=await ek(s,o);break;case er.ox.SORT:a=await eO(s,o);break;case er.ox.PIVOT_TABLE:a=await eT(s,o);break;case er.ox.CONDITIONAL_FORMAT:a=await eF(s,o);break;case er.ox.ADVANCED_VISUALIZATION:a=await en(s,o);break;case er.ox.TABLE:a=await eB(s,o);break;case er.ox.CELL_UPDATE:a=await eP(s,o);break;case er.ox.FORMAT:a=await ez(s,o);break;default:{let e=function(e){let a=e.data||{};return a.formula||a.range?{...e,type:er.ox.FORMULA}:a.chart_type||a.title?{...e,type:er.ox.CHART}:a.data&&Array.isArray(a.data)?{...e,type:er.ox.TABLE}:a.order_by||a.direction?{...e,type:er.ox.SORT}:a.condition||a.filter_column?{...e,type:er.ox.FILTER}:a.background_color||a.text_color||a.format?{...e,type:er.ox.FORMAT}:{...e,type:"GENERIC"}}(o);a=await eZ(s,e)}}if(a&&(s=a.updatedData,"string"==typeof a.resultSummary?r.push(a.resultSummary):Array.isArray(a.resultSummary)&&r.push(String(a.resultSummary)),"modifiedCells"in a&&Array.isArray(a.modifiedCells)&&a.modifiedCells.length>0))for(let e of a.modifiedCells)t.push(e)}catch(s){let a=s instanceof Error?`Erro ao executar opera\xe7\xe3o ${e.type}: ${s.message}`:`Erro desconhecido ao executar opera\xe7\xe3o ${e.type}`;console.error(a,s),o.push(a),r.push(`⚠️ ${a}`)}let l=t.filter((e,a,s)=>a===s.findIndex(a=>a.row===e.row&&a.col===e.col)),n={updatedData:s,resultSummary:r};return l.length>0&&(n.modifiedCells=l),o.length>0&&(n.errors=o),n}async function eZ(e,a){return a.data&&"object"==typeof a.data&&("formula"in a.data||"formula_type"in a.data)?eR(e,{...a,type:er.ox.FORMULA,data:a.data||{}}):a.data&&"object"==typeof a.data&&"chart_type"in a.data?eE(e,{...a,type:er.ox.CHART,data:a.data||{}}):a.data&&"object"==typeof a.data&&"data"in a.data&&Array.isArray(a.data.data)?eB(e,{...a,type:er.ox.TABLE,data:a.data||{}}):{updatedData:e,resultSummary:"Opera\xe7\xe3o gen\xe9rica n\xe3o suportada",modifiedCells:[]}}async function ez(e,a){let{target:s,format:r,decimals:t,locale:o,dateFormat:l,condition:n,value:i,color:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{};d.formatting&&"object"==typeof d.formatting?d.formatting=d.formatting:d.formatting={};let u="";try{if(d.headers&&Array.isArray(d.headers)&&d.rows){if((/^[A-Z]+$/.test(s)?s.charCodeAt(0)-65:d.headers.findIndex(e=>e===s))>=0){d.formatting||(d.formatting={});let e={type:r};"currency"===r?(e.decimals=t||2,e.locale=o||"pt-BR",u=`Coluna ${s} formatada como moeda com ${t||2} casas decimais`):"percentage"===r?(e.decimals=t||0,u=`Coluna ${s} formatada como porcentagem com ${t||0} casas decimais`):"date"===r?(e.dateFormat=l||"dd/mm/yyyy",u=`Coluna ${s} formatada como data no formato ${l||"dd/mm/yyyy"}`):"conditional"===r&&(e.condition=n,e.value=i,e.color=c,u=`Formata\xe7\xe3o condicional aplicada na coluna ${s} (valores ${">"===n?"maiores":"menores"} que ${i} destacados em ${c})`);let a=d.formatting;a[s]=e,d.formatting=a}}else if(s&&"string"==typeof s&&s.includes(":")){d.formatting||(d.formatting={});let e=d.formatting;e[s]={type:r,decimals:t||2,locale:o||"pt-BR",dateFormat:l||"dd/mm/yyyy"},d.formatting=e,u=`Intervalo ${s} formatado como ${r}`}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao aplicar formata\xe7\xe3o:",e),Error(`Falha ao aplicar formata\xe7\xe3o: ${e instanceof Error?e.message:String(e)}`)}}async function eP(e,a){let{cell:s,value:r,_valueType:t}=a.data,o="object"==typeof e&&null!==e?{...e}:{};try{if(o.headers&&Array.isArray(o.headers)&&o.rows&&Array.isArray(o.rows)){let e=s.match(/^[A-Z]+/)?.[0]||"",a=parseInt(s.match(/\d+/)?.[0]||"0",10);if(e&&a>0){let s=e.charCodeAt(0)-65,t=a-1;if(t>=o.rows.length)for(;o.rows.length<=t;)o.rows.push(Array(o.headers.length).fill(""));o.rows[t]&&(o.rows[t][s]=r)}}else o[s]=r;return{updatedData:o,resultSummary:`C\xe9lula ${s} atualizada com o valor "${r}"`}}catch(e){throw console.error("Erro ao atualizar c\xe9lula:",e),Error(`Falha ao atualizar c\xe9lula: ${e instanceof Error?e.message:String(e)}`)}}async function eB(e,a){let{subtype:s,range:r,hasHeaders:t,allData:o,function:l,rowsField:n,columnsField:i,valuesField:c}=a.data,d="object"==typeof e&&null!==e?{...e}:{},u="";try{switch(s){case"CREATE_TABLE":d.tables&&Array.isArray(d.tables)||(d.tables=[]),o?(d.isTable=!0,d.tableHeaders=t,u="Todos os dados foram convertidos em tabela"):r&&Array.isArray(d.tables)&&(d.tables.push({range:String(r),hasHeaders:t}),u=`Intervalo ${String(r)} convertido em tabela`);break;case"ADD_TOTAL_ROW":if(d.tables&&Array.isArray(d.tables)&&0!==d.tables.length)Array.isArray(d.tables)&&d.tables.length>0&&(u="Linha de total adicionada \xe0 tabela");else if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)){let e=d.headers,a=d.rows,s=Array(e.length).fill("");e.forEach((e,r)=>{a.some(e=>"number"==typeof e[r]||"string"==typeof e[r]&&!isNaN(Number(e[r])))&&(s[r]={formula:`=SOMA(${String.fromCharCode(65+r)}2:${String.fromCharCode(65+r)}${a.length+1})`,result:a.reduce((e,a)=>{let s=parseFloat(String(a[r]));return e+(isNaN(s)?0:s)},0)})}),s[0]=s[0]||"Total",d.rows=[...a,s],u="Linha de total adicionada \xe0 tabela"}break;case"PIVOT_TABLE":if(d.headers&&Array.isArray(d.headers)&&d.rows&&Array.isArray(d.rows)&&n&&i&&c){let e=d.headers.findIndex(e=>e===n),a=d.headers.findIndex(e=>e===i),s=d.headers.findIndex(e=>e===c);if(e>=0&&a>=0&&s>=0){let r={},t=new Set,o=new Set;Array.isArray(d.rows)&&d.rows.forEach(l=>{let n=String(l[e]??""),i=String(l[a]??""),c=parseFloat(String(l[s]??"0"))||0;t.add(n),o.add(i),r[n]||(r[n]={}),r[n][i]||(r[n][i]=0),r[n][i]+=c});let l=["",...Array.from(o)],m=Array.from(t).map(e=>{let a=[e];return Array.from(o).forEach(s=>{let t=String(r[e]?.[s]||0);a.push(t)}),a});d.pivotTable={headers:l,rows:m,rowsField:n,columnsField:i,valuesField:c},u=`Tabela din\xe2mica criada com ${n} nas linhas, ${i} nas colunas e ${c} como valores`}}}return{updatedData:d,resultSummary:u}}catch(e){throw console.error("Erro ao executar opera\xe7\xe3o de tabela:",e),Error(`Falha ao executar opera\xe7\xe3o de tabela: ${e instanceof Error?e.message:String(e)}`)}}async function eV(e,a){let s={"financial-expenses":{Data:{required:!0,type:"date"},Valor:{required:!0,type:"number"},Tipo:{required:!0,enum:["Receita","Despesa"]}},"sales-data":{Data:{required:!0,type:"date"},Quantidade:{required:!0,type:"number"},"Valor Unit\xe1rio":{required:!0,type:"number"}},"employee-data":{Nome:{required:!0,type:"string"},Email:{required:!0,type:"email"},"Data Admiss\xe3o":{required:!0,type:"date"}}}[a]||{};for(let a of e)if(Array.isArray(a.data))for(let e of a.data)for(let[a,t]of Object.entries(s)){if(t.required&&(void 0===e[a]||null===e[a]||""===e[a]))throw Error(`Campo obrigat\xf3rio '${a}' est\xe1 vazio`);if(void 0!==e[a]&&null!==e[a]&&""!==e[a]){var r;if("number"===t.type&&isNaN(Number(e[a])))throw Error(`Campo '${a}' deve ser um n\xfamero`);if("email"===t.type&&(r=String(e[a]),!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)))throw Error(`Campo '${a}' deve ser um email v\xe1lido`);if(t.enum&&!t.enum.includes(e[a]))throw Error(`Campo '${a}' deve ser um dos valores: ${t.enum.join(", ")}`)}}}async function eU(e,a){let s={"financial-expenses":{Valor:{type:"currency"},Data:{type:"date"}},"sales-data":{"Valor Unit\xe1rio":{type:"currency"},Total:{type:"currency"},Data:{type:"date"}},"employee-data":{Nome:{type:"uppercase"},Email:{type:"lowercase"},"Data Admiss\xe3o":{type:"date"}}}[a]||{};for(let a of e)Array.isArray(a.data)&&(a.data=a.data.map(e=>{let a={...e};for(let[e,r]of Object.entries(s))if(void 0!==a[e])switch(r.type){case"currency":a[e]=function(e){let a=Number(e);return isNaN(a)?String(e):new Intl.NumberFormat("pt-BR",{style:"currency",currency:"BRL"}).format(a)}(a[e]);break;case"date":a[e]=function(e){try{let a=new Date(e);if(isNaN(a.getTime()))return String(e);return a.toLocaleDateString("pt-BR")}catch{return String(e)}}(a[e]);break;case"uppercase":a[e]=String(a[e]).toUpperCase();break;case"lowercase":a[e]=String(a[e]).toLowerCase()}return a}))}var eq=s(37358),eH=s(24118),eX=s(13635),eG=s(32933);let eY=A.forwardRef(({className:e,...a},s)=>t.jsx(eX.fC,{ref:s,className:(0,k.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...a,children:t.jsx(eX.z$,{className:(0,k.cn)("flex items-center justify-center text-current"),children:t.jsx(eG.Z,{className:"h-4 w-4"})})}));eY.displayName=eX.fC.displayName;var eQ=s(29280),eJ=s(41190),eW=s(44794),eK=s(50384),e0=s(33261);let e1=[{id:"xlsx",name:"Excel (.xlsx)",icon:t.jsx(l.Z,{className:"h-4 w-4"})},{id:"csv",name:"CSV (.csv)",icon:t.jsx(l.Z,{className:"h-4 w-4"})},{id:"json",name:"JSON (.json)",icon:t.jsx(l.Z,{className:"h-4 w-4"})},{id:"pdf",name:"PDF (.pdf)",icon:t.jsx(l.Z,{className:"h-4 w-4"})}];function e3({open:e,onOpenChange:a,workbookId:s,onExport:r}){let[o,l]=(0,A.useState)(["xlsx"]),[n,i]=(0,A.useState)("immediate"),[c,d]=(0,A.useState)(""),[u,m]=(0,A.useState)(""),[p,h]=(0,A.useState)(),[x,f]=(0,A.useState)(!1),[g,y]=(0,A.useState)(!1),[b,v]=(0,A.useState)(!0),[w,N]=(0,A.useState)("{{workbook}}_{{date}}_{{format}}"),j=e=>{l(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},C=o.length>0&&("immediate"===n||c&&u);return t.jsx(eH.Vq,{open:e,onOpenChange:a,children:(0,t.jsxs)(eH.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[t.jsx(eH.fK,{children:(0,t.jsxs)(eH.$N,{className:"flex items-center gap-2",children:[t.jsx(ee.Z,{className:"h-5 w-5"}),"Export em Lote - Configura\xe7\xf5es Avan\xe7adas"]})}),(0,t.jsxs)(eK.mQ,{defaultValue:"formats",className:"space-y-6",children:[(0,t.jsxs)(eK.dr,{className:"grid w-full grid-cols-4",children:[t.jsx(eK.SP,{value:"formats",children:"Formatos"}),t.jsx(eK.SP,{value:"schedule",children:"Agendamento"}),t.jsx(eK.SP,{value:"options",children:"Op\xe7\xf5es"}),t.jsx(eK.SP,{value:"preview",children:"Preview"})]}),t.jsx(eK.nU,{value:"formats",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:t.jsx(X.ll,{className:"text-lg",children:"Selecionar Formatos de Export"})}),(0,t.jsxs)(X.aY,{children:[t.jsx("div",{className:"grid grid-cols-2 gap-4",children:e1.map(e=>(0,t.jsxs)("div",{className:`flex items-center space-x-3 p-4 border rounded-lg cursor-pointer transition-colors ${o.includes(e.id)?"border-primary bg-primary/5":"border-border hover:bg-muted/50"}`,onClick:()=>j(e.id),children:[t.jsx(eY,{checked:o.includes(e.id),onCheckedChange:()=>j(e.id)}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,t.jsx("span",{className:"font-medium",children:e.name})]})]},e.id))}),0===o.length&&t.jsx(e0.bZ,{className:"mt-4",children:t.jsx(e0.X,{children:"Selecione pelo menos um formato para continuar."})})]})]})}),t.jsx(eK.nU,{value:"schedule",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:t.jsx(X.ll,{className:"text-lg",children:"Configurar Agendamento"})}),(0,t.jsxs)(X.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[t.jsx(eW._,{children:"Tipo de Execu\xe7\xe3o"}),(0,t.jsxs)(eQ.Ph,{value:n,onValueChange:e=>i(e),children:[t.jsx(eQ.i4,{children:t.jsx(eQ.ki,{})}),(0,t.jsxs)(eQ.Bw,{children:[t.jsx(eQ.Ql,{value:"immediate",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(W.Z,{className:"h-4 w-4"}),"Executar Agora"]})}),t.jsx(eQ.Ql,{value:"scheduled",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(eq.Z,{className:"h-4 w-4"}),"Agendar Execu\xe7\xe3o"]})})]})]})]}),"scheduled"===n&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"date",children:"Data"}),t.jsx(eJ.I,{id:"date",type:"date",value:c,onChange:e=>d(e.target.value),min:new Date().toISOString().split("T")[0]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"time",children:"Hor\xe1rio"}),t.jsx(eJ.I,{id:"time",type:"time",value:u,onChange:e=>m(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{children:"Recorr\xeancia (Opcional)"}),(0,t.jsxs)(eQ.Ph,{value:p||"",onValueChange:e=>h(e),children:[t.jsx(eQ.i4,{children:t.jsx(eQ.ki,{placeholder:"Selecionar recorr\xeancia"})}),(0,t.jsxs)(eQ.Bw,{children:[t.jsx(eQ.Ql,{value:"",children:"Sem recorr\xeancia"}),t.jsx(eQ.Ql,{value:"daily",children:"Di\xe1rio"}),t.jsx(eQ.Ql,{value:"weekly",children:"Semanal"}),t.jsx(eQ.Ql,{value:"monthly",children:"Mensal"})]})]})]})]})]})]})}),t.jsx(eK.nU,{value:"options",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:t.jsx(X.ll,{className:"text-lg",children:"Op\xe7\xf5es de Export"})}),(0,t.jsxs)(X.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"compression",checked:x,onCheckedChange:e=>f(!0===e)}),t.jsx(eW._,{htmlFor:"compression",children:"Compactar arquivos em ZIP"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"splitBySheets",checked:g,onCheckedChange:e=>y(!0===e)}),t.jsx(eW._,{htmlFor:"splitBySheets",children:"Separar por planilhas"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"includeMetadata",checked:b,onCheckedChange:e=>v(!0===e)}),t.jsx(eW._,{htmlFor:"includeMetadata",children:"Incluir metadados"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"naming",children:"Padr\xe3o de Nomenclatura"}),t.jsx(eJ.I,{id:"naming",value:w,onChange:e=>N(e.target.value),placeholder:"{{workbook}}_{{date}}_{{format}}"}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Vari\xe1veis dispon\xedveis: ","{workbook}",", ","{date}",", ","{time}",", ","{format}",", ","{sheet}"]})]})]})]})}),t.jsx(eK.nU,{value:"preview",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:t.jsx(X.ll,{className:"text-lg",children:"Preview da Configura\xe7\xe3o"})}),t.jsx(X.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[t.jsx(eW._,{className:"text-sm font-medium",children:"Formatos Selecionados:"}),t.jsx("div",{className:"flex flex-wrap gap-2 mt-1",children:o.map(e=>{let a=e1.find(a=>a.id===e);return t.jsx(z.C,{variant:"secondary",children:a?.name},e)})})]}),(0,t.jsxs)("div",{children:[t.jsx(eW._,{className:"text-sm font-medium",children:"Agendamento:"}),t.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"immediate"===n?"Executar imediatamente":`Agendado para ${c} \xe0s ${u}${p?` (${p})`:""}`})]}),(0,t.jsxs)("div",{children:[t.jsx(eW._,{className:"text-sm font-medium",children:"Op\xe7\xf5es:"}),(0,t.jsxs)("ul",{className:"text-sm text-muted-foreground mt-1 space-y-1",children:[x&&t.jsx("li",{children:"• Compacta\xe7\xe3o habilitada"}),g&&t.jsx("li",{children:"• Separar por planilhas"}),b&&t.jsx("li",{children:"• Incluir metadados"}),(0,t.jsxs)("li",{children:["• Padr\xe3o de nome: ",w]})]})]})]})})]})})]}),(0,t.jsxs)(eH.cN,{children:[t.jsx(S.Button,{variant:"outline",onClick:()=>a(!1),children:"Cancelar"}),t.jsx(S.Button,{onClick:()=>{r({formats:o,schedule:"scheduled"===n?{type:"scheduled",datetime:c&&u?new Date(`${c}T${u}`):void 0,recurring:p}:{type:"immediate"},compression:x,splitBySheets:g,includeMetadata:b,customNaming:w})},disabled:!C,children:"immediate"===n?"Exportar Agora":"Agendar Export"})]})]})})}var e2=s(89392),e4=s(9015);function e5({open:e,onOpenChange:a,workbookId:s,workbookName:r,sheets:o,onExport:n}){let[i,c]=(0,A.useState)("xlsx"),[d,u]=(0,A.useState)(!0),[m,p]=(0,A.useState)(!0),[h,x]=(0,A.useState)(!0),[f,g]=(0,A.useState)(""),[y,b]=(0,A.useState)([50]),[v,w]=(0,A.useState)("portrait"),[N,j]=(0,A.useState)("A4"),[C,E]=(0,A.useState)([]),[F,k]=(0,A.useState)("1"),[O,R]=(0,A.useState)(""),[I,T]=(0,A.useState)("#3b82f6"),[_,D]=(0,A.useState)(!0),[L,M]=(0,A.useState)([12]),Z=o.length>0&&o[0]&&Array.isArray(o[0].data)&&o[0].data.length>0?Object.keys(o[0].data[0]):[],z=e=>{E(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])};return t.jsx(eH.Vq,{open:e,onOpenChange:a,children:(0,t.jsxs)(eH.cZ,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[t.jsx(eH.fK,{children:(0,t.jsxs)(eH.$N,{className:"flex items-center gap-2",children:[t.jsx(K.Z,{className:"h-5 w-5"}),"Op\xe7\xf5es Avan\xe7adas de Export - ",r]})}),(0,t.jsxs)(eK.mQ,{defaultValue:"general",className:"space-y-6",children:[(0,t.jsxs)(eK.dr,{className:"grid w-full grid-cols-4",children:[t.jsx(eK.SP,{value:"general",children:"Geral"}),t.jsx(eK.SP,{value:"filters",children:"Filtros"}),t.jsx(eK.SP,{value:"styling",children:"Estilo"}),t.jsx(eK.SP,{value:"security",children:"Seguran\xe7a"})]}),t.jsx(eK.nU,{value:"general",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:(0,t.jsxs)(X.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx(l.Z,{className:"h-5 w-5"}),"Configura\xe7\xf5es Gerais"]})}),(0,t.jsxs)(X.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{children:"Formato de Export"}),(0,t.jsxs)(eQ.Ph,{value:i,onValueChange:e=>c(e),children:[t.jsx(eQ.i4,{children:t.jsx(eQ.ki,{})}),(0,t.jsxs)(eQ.Bw,{children:[t.jsx(eQ.Ql,{value:"xlsx",children:"Excel (.xlsx)"}),t.jsx(eQ.Ql,{value:"csv",children:"CSV (.csv)"}),t.jsx(eQ.Ql,{value:"pdf",children:"PDF (.pdf)"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"headers",checked:d,onCheckedChange:e=>u(!0===e)}),t.jsx(eW._,{htmlFor:"headers",children:"Incluir cabe\xe7alhos"})]}),"xlsx"===i&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"formulas",checked:m,onCheckedChange:e=>p(!0===e)}),t.jsx(eW._,{htmlFor:"formulas",children:"Incluir f\xf3rmulas"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"formatting",checked:h,onCheckedChange:e=>x(!0===e)}),t.jsx(eW._,{htmlFor:"formatting",children:"Incluir formata\xe7\xe3o"})]})]})]}),"pdf"===i&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{children:"Orienta\xe7\xe3o"}),(0,t.jsxs)(eQ.Ph,{value:v,onValueChange:e=>w(e),children:[t.jsx(eQ.i4,{children:t.jsx(eQ.ki,{})}),(0,t.jsxs)(eQ.Bw,{children:[t.jsx(eQ.Ql,{value:"portrait",children:"Retrato"}),t.jsx(eQ.Ql,{value:"landscape",children:"Paisagem"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{children:"Tamanho da P\xe1gina"}),(0,t.jsxs)(eQ.Ph,{value:N,onValueChange:e=>j(e),children:[t.jsx(eQ.i4,{children:t.jsx(eQ.ki,{})}),(0,t.jsxs)(eQ.Bw,{children:[t.jsx(eQ.Ql,{value:"A4",children:"A4"}),t.jsx(eQ.Ql,{value:"A3",children:"A3"}),t.jsx(eQ.Ql,{value:"Letter",children:"Letter"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(eW._,{children:["N\xedvel de Compress\xe3o: ",y[0]||50,"%"]}),t.jsx("div",{className:"w-full",children:t.jsx("input",{type:"range",value:y[0]||50,onChange:e=>b([parseInt(e.target.value)]),max:100,min:0,step:10,className:"w-full"})})]})]})]})}),t.jsx(eK.nU,{value:"filters",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:(0,t.jsxs)(X.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx($.Z,{className:"h-5 w-5"}),"Filtros de Dados"]})}),(0,t.jsxs)(X.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{children:"Colunas a Exportar"}),t.jsx("div",{className:"grid grid-cols-3 gap-2 max-h-40 overflow-y-auto",children:Z.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:`col-${e}`,checked:C.includes(e),onCheckedChange:()=>z(e)}),t.jsx(eW._,{htmlFor:`col-${e}`,className:"text-sm",children:e})]},e))}),0===C.length&&t.jsx("p",{className:"text-sm text-muted-foreground",children:"Nenhuma coluna selecionada = todas as colunas"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"rowStart",children:"Linha Inicial"}),t.jsx(eJ.I,{id:"rowStart",type:"number",value:F,onChange:e=>k(e.target.value),placeholder:"1",min:"1"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"rowEnd",children:"Linha Final"}),t.jsx(eJ.I,{id:"rowEnd",type:"number",value:O,onChange:e=>R(e.target.value),placeholder:"Todas",min:"1"})]})]})]})]})}),t.jsx(eK.nU,{value:"styling",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:(0,t.jsxs)(X.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx(e2.Z,{className:"h-5 w-5"}),"Personaliza\xe7\xe3o de Estilo"]})}),(0,t.jsxs)(X.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"headerColor",children:"Cor do Cabe\xe7alho"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(eJ.I,{id:"headerColor",type:"color",value:I,onChange:e=>T(e.target.value),className:"w-16 h-10"}),t.jsx(eJ.I,{value:I,onChange:e=>T(e.target.value),placeholder:"#3b82f6"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(eY,{id:"alternateRows",checked:_,onCheckedChange:e=>D(!0===e)}),t.jsx(eW._,{htmlFor:"alternateRows",children:"Linhas alternadas"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(eW._,{children:["Tamanho da Fonte: ",L[0]||12,"px"]}),t.jsx("div",{className:"w-full",children:t.jsx("input",{type:"range",value:L[0]||12,onChange:e=>M([parseInt(e.target.value)]),max:24,min:8,step:1,className:"w-full"})})]})]})]})}),t.jsx(eK.nU,{value:"security",className:"space-y-4",children:(0,t.jsxs)(X.Zb,{children:[t.jsx(X.Ol,{children:(0,t.jsxs)(X.ll,{className:"text-lg flex items-center gap-2",children:[t.jsx(e4.Z,{className:"h-5 w-5"}),"Configura\xe7\xf5es de Seguran\xe7a"]})}),(0,t.jsxs)(X.aY,{className:"space-y-4",children:["xlsx"===i&&(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(eW._,{htmlFor:"password",children:"Senha de Prote\xe7\xe3o (Opcional)"}),t.jsx(eJ.I,{id:"password",type:"password",value:f,onChange:e=>g(e.target.value),placeholder:"Digite uma senha para proteger o arquivo"}),t.jsx("p",{className:"text-sm text-muted-foreground",children:"Deixe em branco para n\xe3o proteger o arquivo"})]}),"xlsx"!==i&&t.jsx("p",{className:"text-sm text-muted-foreground",children:"Prote\xe7\xe3o por senha dispon\xedvel apenas para arquivos Excel (.xlsx)"})]})]})})]}),(0,t.jsxs)(eH.cN,{children:[t.jsx(S.Button,{variant:"outline",onClick:()=>a(!1),children:"Cancelar"}),t.jsx(S.Button,{onClick:()=>{n({format:i,includeHeaders:d,includeFormulas:m,includeFormatting:h,password:f||void 0,compression:y[0]||50,pageOrientation:"pdf"===i?v:void 0,pageSize:"pdf"===i?N:void 0,filters:C.length>0||F||O?{columns:C.length>0?C:Z,rows:{start:parseInt(F)||1,end:parseInt(O)||999999}}:void 0,customStyles:{headerColor:I,alternateRows:_,fontSize:L[0]||12}})},children:"Exportar com Op\xe7\xf5es"})]})]})})}function e9({workbookId:e,workbookName:a,sheets:s,variant:r="outline",size:o="sm",enableAdvancedExport:l=!1,allowBatchExport:n=!1}){let{exportExcel:i,isLoading:c}=function(){let[e,a]=(0,A.useState)(!1);return{isLoading:e,importExcel:async(e,s={})=>{if(!e)return null;let r=s.maxSize||10485760;a(!0);let t=N.toast.loading(`Processando arquivo ${e.name}...`);try{if(!function(e){if(["application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel.sheet.binary.macroEnabled.12","application/vnd.ms-excel.sheet.macroEnabled.12"].includes(e.type))return!0;let a=e.name.split(".").pop()?.toLowerCase();return"xls"===a||"xlsx"===a}(e))throw Error("Formato inv\xe1lido: envie um arquivo Excel (.xlsx ou .xls)");if(e.size>r)throw Error(`Arquivo muito grande: o tamanho m\xe1ximo \xe9 ${(r/1048576).toFixed(0)}MB`);s.onProgress?.(10);let a=await e$(e);if(s.onProgress?.(50),!a||0===a.length)throw Error("O arquivo n\xe3o cont\xe9m dados v\xe1lidos");s.validateSchema&&s.template&&(await eV(a,s.template),s.onProgress?.(70)),s.transformData&&s.template&&(await eU(a,s.template),s.onProgress?.(90)),N.toast.success(`${e.name} carregado com sucesso!`,{id:t,duration:3e3}),s.onProgress?.(100),s.trackAnalytics;let o={fileName:e.name,sheets:a};return s.onSuccess&&s.onSuccess(o),o}catch(e){return console.error("Erro ao processar arquivo Excel:",e),N.toast.error("Erro ao importar arquivo",{id:t,description:e instanceof Error?e.message:"N\xe3o foi poss\xedvel processar o arquivo. Tente novamente.",duration:4e3}),null}finally{a(!1)}},exportExcel:async(e,s,r="xlsx",t={})=>{a(!0);let o=s.replace(/[^a-z0-9]/gi,"_").toLowerCase(),l=Date.now(),n=`${o}_${l}`,i=N.toast.loading(`Preparando exporta\xe7\xe3o ${r.toUpperCase()}...`);try{if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");if("xlsx"===r){let a=await eD(e,s);(function(e,a){let s=window.URL.createObjectURL(e),r=document.createElement("a");r.href=s,r.download=a.endsWith(".xlsx")?a:`${a}.xlsx`,document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(s),document.body.removeChild(r)})(a,`${n}.xlsx`),t.trackAnalytics}else"csv"===r&&(function(e,a){if(!e||0===e.length)throw Error("N\xe3o h\xe1 dados para exportar");let s=e[0];if(!s||!s.data)throw Error("Planilha sem dados");let r=s.data,t=e=>{if(null==e||""===e)return"";let a=String(e);return a.includes(",")||a.includes('"')||a.includes("\n")?`"${a.replace(/"/g,'""')}"`:a},o="";if(Array.isArray(r))o=r.map(e=>Array.isArray(e)?e.map(t).join(","):Object.values(e).map(t).join(",")).join("\n");else if("object"==typeof r&&null!==r){let e=[];Object.entries(r).forEach(([a,s])=>{let r=a.match(/([A-Z]+)([0-9]+)/);if(r){let a=et(r,1),t=parseInt(et(r,2),10),o=0;for(let e=0;e<a.length;e++)o=26*o+a.charCodeAt(e)-65+1;for(o--;e.length<=t-1;)e.push([]);if(t>0){for(;e.length<t;)e.push([]);Array.isArray(e[t-1])||(e[t-1]=[]),e[t-1][o]=s}}}),o=e.filter(e=>Array.isArray(e)).map(e=>e.map(e=>t(e??"")).join(",")).join("\n")}let l=new Blob([o],{type:"text/csv;charset=utf-8;"}),n=URL.createObjectURL(l),i=document.createElement("a");i.setAttribute("href",n),i.setAttribute("download",`${a}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}(e,n),t.trackAnalytics);return N.toast.success(`Exporta\xe7\xe3o ${r.toUpperCase()} conclu\xedda`,{id:i,description:`Arquivo "${n}.${r}" baixado com sucesso!`,duration:3e3}),!0}catch(e){return console.error(`Erro ao exportar ${r}:`,e),N.toast.error(`Erro na exporta\xe7\xe3o ${r.toUpperCase()}`,{id:i,description:e instanceof Error?e.message:`N\xe3o foi poss\xedvel exportar para ${r.toUpperCase()}. Tente novamente.`,duration:4e3}),!1}finally{a(!1)}}}}(),[d,u]=(0,A.useState)(!1),[m,p]=(0,A.useState)(!1),h=async()=>{await i(s,a,"xlsx",{trackAnalytics:!0,workbookId:e})},x=async()=>{await i(s,a,"csv",{trackAnalytics:!0,workbookId:e})};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(ea.h_,{children:[t.jsx(ea.$F,{asChild:!0,children:t.jsx(S.Button,{variant:r,size:o,disabled:!s||0===s.length||c,className:"bg-green-600 hover:bg-green-700 text-white flex items-center gap-2",children:c?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Exportando..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(W.Z,{className:"h-4 w-4"}),"Exportar"]})})}),(0,t.jsxs)(ea.AW,{align:"end",children:[(0,t.jsxs)(ea.Xi,{onClick:()=>h(),children:[t.jsx(W.Z,{className:"h-4 w-4 mr-2"}),t.jsx("span",{children:"Exportar como Excel (.xlsx)"})]}),(0,t.jsxs)(ea.Xi,{onClick:()=>x(),children:[t.jsx(W.Z,{className:"h-4 w-4 mr-2"}),t.jsx("span",{children:"Exportar como CSV (.csv)"})]}),l&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(ea.VD,{}),(0,t.jsxs)(ea.Xi,{onClick:()=>u(!0),children:[t.jsx(K.Z,{className:"h-4 w-4 mr-2"}),t.jsx("span",{children:"Op\xe7\xf5es Avan\xe7adas"})]})]}),n&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(ea.VD,{}),(0,t.jsxs)(ea.Xi,{onClick:()=>p(!0),children:[t.jsx(ee.Z,{className:"h-4 w-4 mr-2"}),t.jsx("span",{children:"Export em Lote"})]})]})]})]}),l&&t.jsx(e5,{open:d,onOpenChange:u,workbookId:e,workbookName:a,sheets:s,onExport:e=>{u(!1)}}),n&&t.jsx(e3,{open:m,onOpenChange:p,workbookId:e,onExport:e=>{p(!1)}})]})}var e7=s(45091),e8=s(62783),e6=s(24061),ae=s(3594),aa=s(77109),as=s(56292);process.env.SUPABASE_URL||console.warn("SUPABASE_URL n\xe3o est\xe1 configurada - usando NEXT_PUBLIC_SUPABASE_URL"),process.env.SUPABASE_SERVICE_ROLE_KEY||console.warn("SUPABASE_SERVICE_ROLE_KEY n\xe3o est\xe1 configurada - cliente admin n\xe3o estar\xe1 dispon\xedvel");let ar=(0,as.eI)("https://eliuoignzzxnjkcmmtml.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk",{auth:{autoRefreshToken:!1,persistSession:!1,detectSessionInUrl:!1},db:{schema:"public"}});process.env.SUPABASE_SERVICE_ROLE_KEY&&(0,as.eI)(process.env.SUPABASE_URL||"https://eliuoignzzxnjkcmmtml.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"}});class at{subscribeToWorkbook(e,a){let s=`workbook:${e}`;if(this.channels.has(s))return this.channels.get(s);let r=ar.channel(s).on("postgres_changes",{event:"*",schema:"public",table:"Workbook",filter:`id=eq.${e}`},e=>{a.onWorkbookChange&&a.onWorkbookChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Sheet",filter:`workbookId=eq.${e}`},e=>{a.onSheetChange&&a.onSheetChange({eventType:e.eventType,new:e.new,old:e.old,table:e.table,schema:e.schema})}).on("postgres_changes",{event:"*",schema:"public",table:"Cell",filter:`sheetId=in.(${e})`},s=>{if(a.onCellChange&&s.new){let r=s.new;a.onCellChange({workbookId:e,sheetId:r.sheetId,cellAddress:r.address,value:r.value,userId:r.updatedBy||"unknown",timestamp:r.updatedAt||new Date().toISOString()})}}).subscribe();return this.channels.set(s,r),r}subscribeToUserPresence(e,a,s){let r=`presence:${e}`;if(this.presenceChannels.has(r))return this.presenceChannels.get(r);let t=ar.channel(r,{config:{presence:{key:a.id}}}).on("presence",{event:"sync"},()=>{Object.entries(t.presenceState()).forEach(([a,r])=>{let t=r[0],o=t.cursor;s({userId:a,userName:t.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...o&&{cursor:o}})})}).on("presence",{event:"join"},({key:a,newPresences:r})=>{let t=r[0],o=t.cursor;s({userId:a,userName:t.name,workbookId:e,isOnline:!0,lastSeen:new Date().toISOString(),...o&&{cursor:o}})}).on("presence",{event:"leave"},({key:a,leftPresences:r})=>{let t=r[0],o=t.cursor;s({userId:a,userName:t.name,workbookId:e,isOnline:!1,lastSeen:new Date().toISOString(),...o&&{cursor:o}})}).subscribe(async e=>{"SUBSCRIBED"===e&&await t.track({name:a.name,joinedAt:new Date().toISOString()})});return this.presenceChannels.set(r,t),t}async updateUserCursor(e,a){let s=`presence:${e}`,r=this.presenceChannels.get(s);r&&await r.track({cursor:a,lastActivity:new Date().toISOString()})}async broadcastCellChange(e,a){let s=`workbook:${e}`,r=this.channels.get(s);r&&await r.send({type:"broadcast",event:"cell_change",payload:{...a,timestamp:new Date().toISOString()}})}unsubscribeFromWorkbook(e){let a=`workbook:${e}`,s=this.channels.get(a);s&&(ar.removeChannel(s),this.channels.delete(a))}unsubscribeFromPresence(e){let a=`presence:${e}`,s=this.presenceChannels.get(a);s&&(ar.removeChannel(s),this.presenceChannels.delete(a))}unsubscribeAll(){this.channels.forEach(e=>{ar.removeChannel(e)}),this.channels.clear(),this.presenceChannels.forEach(e=>{ar.removeChannel(e)}),this.presenceChannels.clear()}getConnectionStatus(){return{connected:ar.realtime.isConnected(),activeChannels:this.channels.size,presenceChannels:this.presenceChannels.size}}async reconnectAll(){for(let[,e]of this.channels)e.subscribe();for(let[,e]of this.presenceChannels)e.subscribe()}constructor(){this.channels=new Map,this.presenceChannels=new Map}}let ao=new at;function al(e){let{data:a}=(0,aa.useSession)(),[s,r]=(0,A.useState)(!1),[t,o]=(0,A.useState)(new Map),[l,n]=(0,A.useState)([]);(0,A.useRef)(null),(0,A.useCallback)(e=>{window.dispatchEvent(new CustomEvent("workbook-changed",{detail:e}))},[]),(0,A.useCallback)(e=>{window.dispatchEvent(new CustomEvent("sheet-changed",{detail:e}))},[]),(0,A.useCallback)(e=>{n(a=>[e,...a.slice(0,49)]),window.dispatchEvent(new CustomEvent("cell-changed",{detail:e}))},[]),(0,A.useCallback)(e=>{o(a=>{let s=new Map(a);if(e.isOnline)s.set(e.userId,{userId:e.userId,userName:e.userName,isOnline:!0,lastSeen:e.lastSeen,cursor:e.cursor});else{let a=s.get(e.userId);a&&s.set(e.userId,{...a,isOnline:!1,lastSeen:e.lastSeen})}return s}),window.dispatchEvent(new CustomEvent("user-presence-changed",{detail:e}))},[]);let i=(0,A.useCallback)(async(s,r)=>{if(e&&a?.user)try{await ao.updateUserCursor(e,{sheetId:s,cellAddress:r})}catch(e){console.error("Erro ao atualizar cursor:",e)}},[e,a]),c=(0,A.useCallback)(async(s,r,t)=>{if(e&&a?.user)try{await ao.broadcastCellChange(e,{workbookId:e,sheetId:s,cellAddress:r,value:t,userId:a.user.id||a.user.email||"unknown"})}catch(e){console.error("Erro ao enviar mudan\xe7a de c\xe9lula:",e)}},[e,a]),d=(0,A.useCallback)(()=>ao.getConnectionStatus(),[]),u=(0,A.useCallback)(async()=>{try{await ao.reconnectAll(),r(!0)}catch(e){console.error("Erro ao reconectar:",e),r(!1)}},[]);return{isConnected:s,onlineUsers:Array.from(t.values()),recentChanges:l,updateCursor:i,broadcastCellChange:c,getConnectionStatus:d,reconnect:u,isUserOnline:e=>t.get(e)?.isOnline||!1,getUserCursor:e=>t.get(e)?.cursor,getOnlineCount:()=>Array.from(t.values()).filter(e=>e.isOnline).length}}function an({workbookId:e,className:a=""}){let{onlineUsers:s,isConnected:r,onlineCount:o}=function(e){let{onlineUsers:a,isConnected:s,getOnlineCount:r}=al(e);return{onlineUsers:a,isConnected:s,onlineCount:r()}}(e);return e?(0,t.jsxs)("div",{className:`flex items-center space-x-2 ${a}`,children:[t.jsx(T.pn,{children:(0,t.jsxs)(T.u,{children:[t.jsx(T.aJ,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[r?t.jsx(e7.Z,{className:"h-4 w-4 text-green-500"}):t.jsx(e8.Z,{className:"h-4 w-4 text-red-500"}),t.jsx(z.C,{variant:r?"default":"destructive",className:"text-xs",children:r?"Conectado":"Desconectado"})]})}),t.jsx(T._v,{children:t.jsx("p",{children:r?"Conectado ao Real-time":"Desconectado do Real-time"})})]})}),o>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(e6.Z,{className:"h-4 w-4 text-gray-500"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[o," online"]})]}),t.jsx("div",{className:"flex -space-x-2",children:s.filter(e=>e.isOnline).slice(0,5).map(e=>t.jsx(T.pn,{children:(0,t.jsxs)(T.u,{children:[t.jsx(T.aJ,{asChild:!0,children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(ae.qE,{className:"h-8 w-8 border-2 border-white",children:[t.jsx(ae.F$,{src:`https://api.dicebear.com/7.x/initials/svg?seed=${e.userName}`,alt:e.userName}),t.jsx(ae.Q5,{className:"text-xs",children:e.userName.substring(0,2).toUpperCase()})]}),t.jsx("div",{className:"absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white"})]})}),t.jsx(T._v,{children:(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("p",{className:"font-medium",children:e.userName}),t.jsx("p",{className:"text-xs text-gray-500",children:"Online agora"}),e.cursor&&(0,t.jsxs)("p",{className:"text-xs text-blue-500",children:["Editando: ",e.cursor.cellAddress]})]})})]})},e.userId))}),o>5&&t.jsx(T.pn,{children:(0,t.jsxs)(T.u,{children:[t.jsx(T.aJ,{asChild:!0,children:(0,t.jsxs)("div",{className:"flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 border-2 border-white text-xs font-medium text-gray-600",children:["+",o-5]})}),t.jsx(T._v,{children:(0,t.jsxs)("p",{children:["Mais ",o-5," usu\xe1rios online"]})})]})})]}):null}var ai=s(80440);let ac=ai.fC;ai.xz;let ad=ai.h_,au=A.forwardRef(({className:e,...a},s)=>t.jsx(ai.aV,{className:(0,k.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:s}));au.displayName=ai.aV.displayName;let am=A.forwardRef(({className:e,...a},s)=>(0,t.jsxs)(ad,{children:[t.jsx(au,{}),t.jsx(ai.VY,{ref:s,className:(0,k.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a})]}));am.displayName=ai.VY.displayName;let ap=({className:e,...a})=>t.jsx("div",{className:(0,k.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...a});ap.displayName="AlertDialogHeader";let ah=({className:e,...a})=>t.jsx("div",{className:(0,k.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});ah.displayName="AlertDialogFooter";let ax=A.forwardRef(({className:e,...a},s)=>t.jsx(ai.Dx,{ref:s,className:(0,k.cn)("text-lg font-semibold",e),...a}));ax.displayName=ai.Dx.displayName;let af=A.forwardRef(({className:e,...a},s)=>t.jsx(ai.dk,{ref:s,className:(0,k.cn)("text-sm text-muted-foreground",e),...a}));af.displayName=ai.dk.displayName,A.forwardRef(({className:e,...a},s)=>t.jsx(ai.aU,{ref:s,className:(0,k.cn)((0,S.d)(),e),...a})).displayName=ai.aU.displayName;let ag=A.forwardRef(({className:e,...a},s)=>t.jsx(ai.$j,{ref:s,className:(0,k.cn)((0,S.d)({variant:"outline"}),"mt-2 sm:mt-0",e),...a}));ag.displayName=ai.$j.displayName;var ay=s(50258),ab=s(37568),av=s(44099),aw=s(28612);!function(e){e.IDLE="idle",e.PENDING="pending",e.COMPLETED="completed",e.FAILED="failed"}(r||(r={}));let aA=(e={})=>{let[a,s]=(0,A.useState)(e.initialMessages||[]),[r,t]=(0,A.useState)(!1),[o,l]=(0,A.useState)(null),[n,i]=(0,A.useState)("idle"),[c,d]=(0,A.useState)(null),u=(0,A.useRef)(null);(0,A.useEffect)(()=>{o&&a.length>0&&l(null)},[a,o]);let m=e.useMock||!1,p=(0,A.useCallback)(async e=>new Promise(a=>{setTimeout(()=>{a(function(e){let a=[{keywords:["m\xe9dia","coluna"],response:`{
        "operations": [
          {
            "type": "FORMULA",
            "data": {
              "formula": "=M\xc9DIA(B:B)",
              "range": "C1"
            }
          }
        ],
        "explanation": "Calculando a m\xe9dia da coluna B",
        "interpretation": "Voc\xea solicitou o c\xe1lculo da m\xe9dia dos valores na coluna B"
      }`},{keywords:["gr\xe1fico","barras"],response:`{
        "operations": [
          {
            "type": "CHART",
            "data": {
              "type": "bar",
              "title": "Gr\xe1fico de Barras",
              "labels": "A1:A10",
              "datasets": ["B1:B10"]
            }
          }
        ],
        "explanation": "Criando um gr\xe1fico de barras com dados das colunas A e B",
        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de um gr\xe1fico de barras usando os dados existentes"
      }`},{keywords:["tabela","criar"],response:`{
        "operations": [
          {
            "type": "CELL_UPDATE",
            "data": {
              "updates": [
                { "cell": "A1", "value": "Produto" },
                { "cell": "B1", "value": "Valor" },
                { "cell": "C1", "value": "Quantidade" },
                { "cell": "A2", "value": "Produto 1" },
                { "cell": "B2", "value": 100 },
                { "cell": "C2", "value": 10 }
              ]
            }
          }
        ],
        "explanation": "Criando uma tabela com 3 colunas: Produto, Valor e Quantidade",
        "interpretation": "Voc\xea solicitou a cria\xe7\xe3o de uma nova tabela para registro de produtos"
      }`}],s=e.toLowerCase(),r=a.filter(e=>e.keywords.some(e=>s.includes(e)));return r.length>0&&r[0]?r[0].response:`{
    "operations": [
      {
        "type": "CELL_UPDATE",
        "data": {
          "updates": [
            { "cell": "A1", "value": "Exemplo" },
            { "cell": "B1", "value": 100 }
          ]
        }
      }
    ],
    "explanation": "Realizando uma opera\xe7\xe3o exemplo baseada no seu comando",
    "interpretation": "Seu comando foi processado como uma solicita\xe7\xe3o de exemplo"
  }`}(e))},1500)}),[]);(0,A.useCallback)(async a=>{try{let s;if(!a||""===a.trim())return null;if(m)s=await p(a);else try{s=(await av.Z.post("/api/ai/chat",{message:a,userId:e.workbookId||"anonymous",context:{mode:"preview",excelContext:{activeSheet:"Atual"}},preserveContext:!1})).data}catch(e){return console.error("Erro na comunica\xe7\xe3o com Gemini durante interpreta\xe7\xe3o:",e),null}try{let e=JSON.parse(s);if(e.interpretation)return{interpretation:e.interpretation,confidence:e.confidence||0,commandId:(0,k.x0)(),_commandId:(0,k.x0)()}}catch(e){console.error("Erro ao parsear resposta de interpreta\xe7\xe3o:",e)}return null}catch(e){return console.error("Erro ao interpretar comando:",e),null}},[e.workbookId,m,p]);let h=(0,A.useCallback)(async r=>{if(!r.trim())return;let o={id:`user-${Date.now()}`,content:r,role:"user",timestamp:new Date};s(e=>[...e,o]),t(!0),l(null);try{let r=await fetch("/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[...a,o].map(e=>({role:e.role,content:e.content})),modelName:e.modelName,systemPrompt:e.systemPrompt})});if(!r.ok)throw Error(`Error: ${r.statusText}`);let t=await r.json(),l={id:`assistant-${Date.now()}`,content:t.response,role:"assistant",timestamp:new Date};s(e=>[...e,l])}catch(s){let a=s instanceof Error?s:Error("Unknown error");l(a),e.onError&&e.onError(a)}finally{t(!1)}},[a,e]),x=(0,A.useCallback)(async r=>{try{let o;t(!0);let l={id:`user-${Date.now()}`,content:r,role:"user",timestamp:new Date};if(s(a=>{let s=[...a,l],r=e.maxHistorySize||20;return s.length>r?s.slice(-r):s}),m)o=await p(r);else try{let s=e.maxHistorySize||20,t=a.slice(-Math.min(s,10)).map(e=>({role:e.role,content:e.content})),l=e.workbookId;o=(await av.Z.post("/api/ai/chat",{message:r,userId:l||"anonymous",context:l?{excelContext:{activeSheet:"Atual"},responseStructure:{preferJson:!0}}:{},preserveContext:t.length>0})).data}catch(a){console.error("Erro na comunica\xe7\xe3o direta com Gemini, tentando API:",a),o=(await av.Z.post("/api/chat",{message:r,workbookId:e.workbookId})).data.response}let n={id:`assistant-${Date.now()}`,content:o,role:"assistant",timestamp:new Date};s(a=>{let s=[...a,n],r=e.maxHistorySize||20;return s.length>r?s.slice(-r):s}),e.onMessageReceived&&e.onMessageReceived(o);let c=(0,k.x0)();try{await aw.M.storeFeedback({commandId:c,command:r,successful:!0})}catch(e){console.error("Erro ao armazenar comando para feedback:",e)}if(o){let a={interpretation:o,confidence:1,commandId:(0,k.x0)(),_commandId:(0,k.x0)()};d(a),i("pending"),e.onInterpretation&&e.onInterpretation(a)}return{response:o,commandId:c}}catch(e){throw console.error("Erro ao executar comando:",e),e}finally{t(!1)}},[a,e.workbookId,m,p,e.onMessageReceived,e.maxHistorySize,s,t,aw.M,e.onInterpretation]),f=(0,A.useCallback)(async()=>{if(!c)return null;let{_commandId:e}=c,a=await x(c.interpretation);return d(null),a},[c,x,d]),g=(0,A.useCallback)(()=>{d(null),i("idle")},[]),y=(0,A.useCallback)(()=>{s([]),d(null),i("idle")},[]);return(0,A.useEffect)(()=>{let e=u.current;return()=>{e&&e.abort()}},[]),{messages:a,isProcessing:r,error:o,sendMessage:h,clearMessages:y,confirmAndExecute:f,cancelCommand:g,pendingInterpretation:c,commandStatus:n}};var aN=s(37178),aj=s(39404);let aC=new Map,aE=0,aS=0,aF=(0,A.memo)(({rowIndex:e,colIndex:a,value:s,isModified:r,readOnly:o,onCellChange:l,header:n})=>{let i=`${e}-${a}`;null!=s&&aC.set(i,String(s));let c=s??(()=>{let e=aC.get(i);return e?(aS++,e):(aE++,"")})(),d=(0,A.useCallback)(s=>{let r=s.target.value;aC.set(i,r),l(e,a,r)},[e,a,l,i]),u=r?"bg-blue-50 dark:bg-blue-900/30 transition-colors duration-1000":"",m=(0,A.useMemo)(()=>`table-cell p-1 border border-border ${u}`,[u]);return t.jsx("div",{className:m,role:"gridcell","aria-colindex":a+1,"aria-rowindex":e+1,children:t.jsx("input",{type:"text",value:c,readOnly:o,onChange:d,className:"w-full bg-transparent border-0 focus:ring-1 focus:ring-blue-500 p-1","aria-label":`C\xe9lula ${n}${e+1}`})})},(e,a)=>e.value===a.value&&e.isModified===a.isModified&&e.readOnly===a.readOnly);aF.displayName="OptimizedCell";let ak=(0,A.memo)(({visibleRows:e,totalRows:a,virtualizer:s})=>((0,A.useEffect)(()=>{if(a>1e3){let e=Array.from(aC.keys()),r=new Set,t=Math.max(0,s.range.startIndex-20),o=Math.min(a-1,s.range.endIndex+20);for(let e=t;e<=o;e++)r.add(e);e.forEach(e=>{let a=e.split("-")[0];if(a){let s=parseInt(a,10);r.has(s)||aC.delete(e)}})}},[e,a,s]),null));ak.displayName="VirtualizedRowWrapper";let aO=(0,A.memo)(({rowIndex:e,rowData:a,headers:s,modifiedCellsMap:r,readOnly:o,onCellChange:l,onRemoveRow:n})=>{let i=(0,A.useCallback)(()=>{n(e)},[e,n]),c=(0,A.useMemo)(()=>a.map((a,n)=>{let i=`${e}-${n}`,c=r[i]||!1;return t.jsx(aF,{rowIndex:e,colIndex:n,value:a,isModified:c,readOnly:o,onCellChange:l,header:s[n]||""},i)}),[a,e,r,o,l,s]),u=(0,A.useMemo)(()=>o?null:t.jsx("div",{className:"table-cell w-10 text-center",children:t.jsx(S.Button,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:i,"aria-label":`Remover linha ${e+1}`,children:t.jsx(d.Z,{className:"h-3 w-3"})})}),[o,i,e]);return(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"table-cell w-10 text-center text-xs text-muted-foreground bg-muted",children:e+1}),c,u]})},(e,a)=>{if(e.readOnly!==a.readOnly||e.rowIndex!==a.rowIndex||e.rowData.length!==a.rowData.length||e.headers.length!==a.headers.length)return!1;if(e.rowData.length<=10){for(let s=0;s<e.rowData.length;s++){if(e.rowData[s]!==a.rowData[s])return!1;let r=`${e.rowIndex}-${s}`;if(e.modifiedCellsMap[r]!==a.modifiedCellsMap[r])return!1}return!0}{let s=Object.keys(e.modifiedCellsMap).filter(a=>a.startsWith(`${e.rowIndex}-`)&&e.modifiedCellsMap[a]),r=Object.keys(a.modifiedCellsMap).filter(e=>e.startsWith(`${a.rowIndex}-`)&&a.modifiedCellsMap[e]);return!(s.length!==r.length||s.some(e=>!a.modifiedCellsMap[e]))&&JSON.stringify(e.rowData)===JSON.stringify(a.rowData)}});aO.displayName="OptimizedRow";let aR=(e,a)=>{let s={};return e&&e.length>0?e.forEach(e=>{s[`${e.row}-${e.col}`]=!0}):a&&(s[`${a.row}-${a.col}`]=!0),function(e=1e4){aC.size>e&&Array.from(aC.keys()).slice(0,1e3).forEach(e=>aC.delete(e))}(),s},aI=[{text:"Crie uma tabela de controle de horas",icon:t.jsx(l.Z,{className:"h-3 w-3"})},{text:"Adicione valida\xe7\xe3o de dados na coluna B",icon:t.jsx(n.Z,{className:"h-3 w-3"})},{text:"Gere um gr\xe1fico de barras com os dados",icon:t.jsx(i.Z,{className:"h-3 w-3"})},{text:"Calcule a m\xe9dia da coluna C",icon:t.jsx(n.Z,{className:"h-3 w-3"})},{text:"Formate a tabela com cores alternadas",icon:t.jsx(n.Z,{className:"h-3 w-3"})}],aT=[{title:"Bem-vindo ao Excel Copilot",content:"Este assistente permite criar e editar planilhas atrav\xe9s de comandos em linguagem natural.",target:"header"},{title:"Assistente de IA",content:"Aqui voc\xea pode digitar comandos como 'Crie uma tabela de vendas' ou 'Calcule a m\xe9dia da coluna B'.",target:"ai-assistant"},{title:"Sugest\xf5es R\xe1pidas",content:"Exemplos de comandos que voc\xea pode usar. Clique em um deles para executar.",target:"suggestions"},{title:"Planilha Interativa",content:"Sua planilha ser\xe1 atualizada automaticamente conforme seus comandos. Voc\xea tamb\xe9m pode editar c\xe9lulas manualmente.",target:"spreadsheet"}],a_=(0,A.memo)(({command:e,onClick:a})=>(0,t.jsxs)(S.Button,{variant:"ghost",className:"h-8 px-2 text-sm justify-start w-full hover:bg-accent",onClick:a,children:[t.jsx("span",{className:"mr-2",children:e.icon}),t.jsx("span",{className:"truncate",children:e.text})]}));function aD({workbookId:e,initialData:a,readOnly:r=!1,onSave:l,initialCommand:i}){let[j,C]=(0,A.useState)(a||{headers:["A","B","C"],rows:[["","",""],["","",""],["","",""]],charts:[],name:"Nova Planilha"}),[E,F]=(0,A.useState)([]),[k,O]=(0,A.useState)(-1),[R,I]=(0,A.useState)(!1),[T,_]=(0,A.useState)(!1),[D,$]=(0,A.useState)(!1),[L,M]=(0,A.useState)(!1),[Z,z]=(0,A.useState)(null),[B,V]=(0,A.useState)(!1),[q,H]=(0,A.useState)(!1),[X,Y]=(0,A.useState)(!1),[Q,W]=(0,A.useState)(0),[K,ee]=(0,A.useState)(!1);(0,A.useRef)(null);let ea=(0,A.useRef)([]),[es,er]=(0,A.useState)(null),[et,eo]=(0,A.useState)(null),[el,en]=(0,A.useState)(!1),[ei,ed]=(0,A.useState)(null),[eu,em]=(0,A.useState)([]),ep=(0,A.useRef)(null),eh=(0,A.useCallback)((e,a)=>{let s=setTimeout(()=>{e(),ea.current=ea.current.filter(e=>e!==s)},a);return ea.current.push(s),s},[]),ex=(0,A.useCallback)(e=>{if(E.length>0&&JSON.stringify(E[E.length-1])===JSON.stringify(e))return;let a=E.slice(0,k+1).slice(-19);F([...a,JSON.parse(JSON.stringify(e))]),O(a.length)},[E,k]),{processExcelCommand:ef,isProcessing:eg,lastModifiedCells:ey}=function({onDataChange:e,onAddHistory:a}={}){let[r,t]=(0,A.useState)(!1),[o,l]=(0,A.useState)([]),[n,i]=(0,A.useState)([]),c=(0,A.useRef)(null),{executeOperation:d,isProcessing:u,cancelAllOperations:m}=function(e={}){let a=(0,A.useRef)(null),[s,r]=(0,A.useState)(!1),t=(0,A.useRef)(new Map);return{executeOperation:(0,A.useCallback)(async(e,s)=>{if(!a.current)try{r(!0);let a={updatedData:s,resultSummary:`Opera\xe7\xe3o ${e.type} executada (fallback)`,modifiedCells:[]};return r(!1),a}catch(e){throw r(!1),e}let o=(0,aj.x0)();return r(!0),new Promise((r,l)=>{t.current.set(o,{operation:e,resolve:r,reject:l}),a.current.postMessage({operation:e,sheetData:s,requestId:o,operationType:e.type})})},[]),isProcessing:s,cancelAllOperations:(0,A.useCallback)(()=>{t.current.forEach((e,a)=>{e.reject(Error("Opera\xe7\xe3o cancelada"))}),t.current.clear(),r(!1)},[])}}({onSuccess:e=>{e.modifiedCells&&(l(e.modifiedCells),p())},onError:e=>{ec.logger.error("Erro no worker Excel:",e)}}),p=(0,A.useCallback)(()=>{c.current&&clearTimeout(c.current),c.current=setTimeout(()=>{l([]),c.current=null},3e3)},[]),h=(0,A.useCallback)(e=>e.length>2||e.some(e=>{let a=e.type?String(e.type).toUpperCase():void 0;return"FILTER"===a||"SORT"===a||"PIVOT_TABLE"===a||"CHART"===a||"ADVANCED_VISUALIZATION"===a}),[]);return{processExcelCommand:(0,A.useCallback)(async(o,n)=>{if(!o||!n)return null;if(r||u)return N.toast.info("Aguarde a conclus\xe3o da opera\xe7\xe3o anterior",{duration:2e3}),null;t(!0);try{a&&a(structuredClone(n));try{let{createExcelAIProcessor:a}=await Promise.resolve().then(s.bind(s,96671));a();let r={activeSheet:n.name,headers:n.headers,selection:`A1:${String.fromCharCode(65+n.headers.length-1)}${n.rows.length}`,recentOperations:[]},t=new(await Promise.resolve().then(s.bind(s,96671))).ExcelAIProcessor(r),c=await t.processQuery(o);if(c.operations&&c.operations.length>0&&!c.error){if((n.rows.length>500||n.rows.length>0&&n.rows[0]&&n.rows[0].length>50)&&h(c.operations))try{let a=await Promise.all(c.operations.map(e=>d(e,n))),s=structuredClone(n),r=[],t=[];for(let e of a)s=e.updatedData,r.push(String(e.resultSummary)),e.modifiedCells&&t.push(...e.modifiedCells);return i(r),t.length>0&&(l(t),p()),e&&e(s),N.toast.success("Opera\xe7\xf5es executadas",{description:r.join("; "),duration:3e3}),s}catch(e){(0,aN.KE)("Erro no worker, retornando ao m\xe9todo padr\xe3o:",e)}let a=await eM(n,c.operations),s={updatedData:a.updatedData,resultSummary:Array.isArray(a.resultSummary)?a.resultSummary:[String(a.resultSummary)],modifiedCells:a.modifiedCells};return i(s.resultSummary),s.modifiedCells&&(l(s.modifiedCells),p()),e&&e(s.updatedData),N.toast.success("Opera\xe7\xf5es executadas",{description:s.resultSummary.join("; "),duration:3e3}),s.updatedData}}catch(e){(0,aN.KE)("Erro no novo processor, tentando fallback:",e)}try{let a=await eL(o);if(!a.success||!(a.operations.length>0))return N.toast.info("Nenhuma opera\xe7\xe3o Excel",{description:a.message||"N\xe3o foi poss\xedvel extrair opera\xe7\xf5es Excel deste comando.",duration:4e3}),null;{let s=await eM(n,a.operations),r={updatedData:s.updatedData,resultSummary:Array.isArray(s.resultSummary)?s.resultSummary:[String(s.resultSummary)],modifiedCells:s.modifiedCells};return i(r.resultSummary),r.modifiedCells&&(l(r.modifiedCells),p()),e&&e(r.updatedData),N.toast.success("Opera\xe7\xf5es executadas",{description:r.resultSummary.join("; "),duration:3e3}),r.updatedData}}catch(e){(0,aN.H)("Erro no parser de comandos:",e)}return N.toast.error("N\xe3o foi poss\xedvel executar o comando",{description:"Tente reformular seu comando ou use um exemplo da lista de sugest\xf5es.",duration:4e3}),null}catch(e){return(0,aN.H)("Erro ao processar comando Excel:",e),N.toast.error("Erro ao processar comando",{description:e instanceof Error?e.message:"Ocorreu um erro desconhecido.",duration:4e3}),null}finally{t(!1)}},[r,u,e,a,p,d,h]),isProcessing:r||u,lastModifiedCells:o,lastOperationSummary:n}}({onDataChange:C,onAddHistory:ex}),{isConnected:eb,updateCursor:ev,broadcastCellChange:ew}=al(e);(0,A.useCallback)(e=>ef(e,j),[ef,j]);let eA=(0,A.useCallback)(e=>{er(e.interpretation),eo({id:e.commandId||e._commandId||"",command:e.interpretation})},[]),eN=(0,A.useCallback)(a=>{a&&Array.isArray(a)&&0!==a.length&&(ex(j),C(s=>{let r={...s};r.headers=Array.isArray(s.headers)?[...s.headers]:[],r.rows=Array.isArray(s.rows)?[...s.rows]:[];let t=[];return a.forEach(a=>{if(a&&"object"==typeof a){if("cell_update"===a.type&&"number"==typeof a.row&&"number"==typeof a.col){if(Array.isArray(r.rows[a.row])||(r.rows[a.row]=Array(r.headers.length).fill("")),a.row>=0&&a.col>=0&&Array.isArray(r.headers)&&a.col<r.headers.length&&Array.isArray(r.rows)&&void 0!==r.rows[a.row]&&Array.isArray(r.rows[a.row]))try{r.rows[a.row]&&"number"==typeof a.col&&(r.rows[a.row][a.col]=a.value,t.push({row:a.row,col:a.col}))}catch(s){ec.logger.error("SpreadsheetEditor: Erro ao atualizar c\xe9lula",s,{row:a.row,col:a.col,operation:a.type,workbookId:e})}}else if("add_row"===a.type){let e=Array(r.headers.length).fill("");r.rows.push(e)}else"add_column"===a.type&&Array.isArray(r.headers)&&Array.isArray(r.rows)&&(r.headers.push(a.name||`Coluna ${r.headers.length+1}`),r.rows.forEach((e,a)=>{Array.isArray(e)?r.rows[a]=[...e,""]:r.rows[a]=Array(r.headers.length).fill("")}))}}),t.length>0&&z(t[0]||null),r}))},[j,ex,z]),{sendMessage:ej,isProcessing:eC,confirmAndExecute:eE,cancelCommand:eS,pendingInterpretation:eF,messages:ek,error:eO,clearMessages:eR,commandStatus:eI}=aA({workbookId:e,onMessageReceived:a=>{if(a)try{let e=JSON.parse(a);e.operations&&eN(e.operations)}catch(s){ec.logger.error("SpreadsheetEditor: Erro ao processar resposta da IA",s,{content:a?.substring(0,100),workbookId:e})}},onInterpretation:eA}),[eT,e_]=(0,A.useState)(null),[eD,e$]=(0,A.useState)(!1),eZ=(0,w.useRouter)(),ez=(0,A.useCallback)(()=>{k>0&&(O(k-1),C(JSON.parse(JSON.stringify(E[k-1]))),N.toast.info("A\xe7\xe3o desfeita"))},[E,k]),eP=(0,A.useCallback)(()=>{k<E.length-1&&(O(k+1),C(JSON.parse(JSON.stringify(E[k+1]))),N.toast.info("A\xe7\xe3o refeita"))},[E,k]),eB=(0,A.useCallback)(async()=>{if(!r)try{if(I(!0),l){await l(j),N.toast.success("Planilha salva com sucesso");return}if(!(await fetch(`/api/workbooks/${e}/sheets`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:j.name||"Sem nome",data:JSON.stringify(j)})})).ok)throw Error("Erro ao salvar planilha");N.toast.success("Planilha salva com sucesso")}catch(a){ec.logger.error("SpreadsheetEditor: Erro ao salvar planilha",a,{workbookId:e,spreadsheetName:j.name,readOnly:r}),N.toast.error("Erro ao salvar planilha")}finally{I(!1)}},[e,j,l,r]),eV=(0,A.useCallback)(e=>{e.trim()&&ej(e)},[ej]),eU=(0,A.useMemo)(()=>aR(ey,Z),[ey,Z]),eq=(0,A.useCallback)(async(e,a,s)=>{if(!r&&"number"==typeof e&&"number"==typeof a&&!(e<0)&&!(a<0)&&(ex(j),C(r=>{if(!r||!Array.isArray(r.headers))return r;let t={...r};return t.rows=Array.isArray(r.rows)?[...r.rows]:[],Array.isArray(t.rows[e])?t.rows[e]=[...t.rows[e]]:t.rows[e]=Array(t.headers.length).fill(""),a<t.headers.length&&(t.rows[e][a]=s),t}),z({row:e,col:a}),eb))try{let r=`${String.fromCharCode(65+a)}${e+1}`;await ew("sheet1",r,s),await ev("sheet1",r)}catch(e){console.error("Erro ao enviar mudan\xe7a via Real-time:",e)}},[r,ex,j,z,eb,ew,ev]),eH=()=>{r||(ex(j),C(e=>{let a;let s=e.headers.length>0?e.headers[e.headers.length-1]:null;a=s&&/^[A-Z]$/.test(s)?String.fromCharCode((s.charCodeAt(0)||64)+1):`Coluna ${e.headers.length+1}`;let r={...e};return r.headers=[...e.headers,a],r.rows=Array.isArray(e.rows)?e.rows.map(e=>Array.isArray(e)?[...e,""]:Array(r.headers.length).fill("")):[],r}),N.toast.success("Coluna adicionada"))},eX=(0,A.useCallback)(()=>{if(r)return;ex(j);let e=Array(j.headers.length).fill("");C(a=>{let s={...a};return s.rows=Array.isArray(a.rows)?[...a.rows,e]:[e],s}),N.toast.success("Linha adicionada")},[r,j,ex]),eG=e=>{if(r)return;ex(j);let a=[...j.rows];a.splice(e,1),C({...j,rows:a})},eY=e=>{if(r)return;ex(j);let a=[...j.headers];a.splice(e,1);let s=j.rows.map(a=>{let s=[...a];return s.splice(e,1),s});C({...j,headers:a,rows:s})};(0,A.useCallback)(e=>{if(eg||r){N.toast.info("Aguarde",{description:"Espere o comando atual terminar antes de enviar outro.",duration:2e3});return}ej(e),N.toast.success("Comando enviado",{description:`Executando: "${e}"`,duration:2e3}),T&&_(!1)},[eg,r,ej,T]),(0,A.useCallback)(()=>{_(!0)},[]),(0,A.useCallback)(e=>{if(R||eg){N.toast.info("Aguarde",{description:"Concluindo opera\xe7\xf5es atuais antes de navegar...",duration:2e3});return}E.length>1&&E[k]!==a?confirm("H\xe1 altera\xe7\xf5es n\xe3o salvas. Deseja sair mesmo assim?")&&(window.location.href=e):window.location.href=e},[E,k,a,R,eg]),(0,A.useCallback)(()=>{N.toast.info("Conectando com Excel Desktop",{description:"Iniciando conex\xe3o com o aplicativo Excel...",duration:3e3}),desktopBridge.connect().then(e=>{e?N.toast.success("Excel conectado",{description:"Sincroniza\xe7\xe3o de dados ativada entre o navegador e Excel desktop",duration:3e3}):N.toast.error("Falha na conex\xe3o",{description:"N\xe3o foi poss\xedvel conectar ao Excel. Verifique se o Excel Copilot Desktop est\xe1 instalado e em execu\xe7\xe3o.",duration:4e3})}).catch(e=>{console.error("Erro na conex\xe3o:",e),N.toast.error("Erro na conex\xe3o",{description:"Ocorreu um erro ao tentar conectar ao Excel.",duration:4e3})})},[desktopBridge]),(0,A.useCallback)(()=>{H(!0),eh(()=>{H(!1)},3e3)},[eh]),(0,A.useCallback)(()=>{Q<aT.length-1?W(Q+1):Y(!1)},[Q]),(0,A.useCallback)(()=>{Y(!1)},[]);let[eQ,eJ]=(0,A.useState)(!1),[eW,eK]=(0,A.useState)(!1),e0=(0,A.useCallback)(()=>[{key:"s",description:"Salvar planilha",action:eB,modifiers:{ctrl:!0}},{key:"z",description:"Desfazer \xfaltima a\xe7\xe3o",action:ez,modifiers:{ctrl:!0}},{key:"y",description:"Refazer \xfaltima a\xe7\xe3o",action:eP,modifiers:{ctrl:!0}},{key:"+",description:"Adicionar linha",action:eX,modifiers:{ctrl:!0,shift:!0}},{key:"=",description:"Adicionar coluna",action:eH,modifiers:{ctrl:!0,shift:!0}},{key:"k",description:"Abrir paleta de comandos",action:()=>_(!0),modifiers:{ctrl:!0}},{key:"/",description:"Focar no chat assistente",action:()=>document.getElementById("chat-input")?.focus(),modifiers:{ctrl:!0}},{key:"f",description:"Alternar modo tela cheia",action:()=>eK(!eW),modifiers:{ctrl:!0,shift:!0}},{key:"Escape",description:"Fechar di\xe1logos/pain\xe9is abertos",action:()=>{_(!1),eJ(!1),eW&&eK(!1)}}],[ez,eP,eX,eH,eW]);(0,A.useCallback)(e=>{if("INPUT"!==e.target.tagName&&"TEXTAREA"!==e.target.tagName&&!e.target.isContentEditable){for(let a of e0())if(e.key.toLowerCase()===a.key.toLowerCase()&&(!a.modifiers?.ctrl||e.ctrlKey)&&(!a.modifiers?.alt||e.altKey)&&(!a.modifiers?.shift||e.shiftKey)){e.preventDefault(),a.action();return}}},[e0]);let[e1,e3]=(0,A.useState)(!0),[e2,e4]=(0,A.useState)(""),e5=e=>{e4(e),e3(""===e.trim())},e7=()=>{eZ.push("/pricing")},e8=()=>{eZ.push("/api/checkout/trial")},e6=(0,A.useMemo)(()=>aI.map((e,a)=>t.jsx(a_,{command:e,onClick:()=>eV(e.text)},a)),[eV]),ae=(0,A.useRef)(null),aa=(0,A.useRef)(null),as=(0,o.MG)({count:j.rows.length,getScrollElement:()=>ae.current,estimateSize:()=>36,overscan:10}),ar=(0,A.useMemo)(()=>t.jsx(ak,{visibleRows:as.getVirtualItems().length,totalRows:j.rows.length,virtualizer:as}),[as,j.rows.length]),at=(0,A.useCallback)(async()=>{if(ep.current&&et){er(null);try{await ep.current.confirmAndExecute()&&(ed(et),en(!0),setTimeout(()=>{el&&en(!1)},5e3))}catch(e){console.error("Erro ao executar comando:",e),N.toast.error("Erro ao executar o comando")}finally{eo(null)}}},[et,ep]),ao=(0,A.useCallback)(()=>{ep.current&&(ep.current.cancelCommand(),er(null),eo(null))},[ep]),ai=(0,A.useCallback)(async e=>{try{await aw.M.storeFeedback(e),en(!1),ed(null)}catch(e){console.error("Erro ao enviar feedback:",e),N.toast.error("N\xe3o foi poss\xedvel enviar seu feedback")}},[]),ad=(0,A.useCallback)(()=>{en(!1),ed(null)},[]);return(0,t.jsxs)("div",{className:"flex flex-col h-full w-full relative",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center border-b p-2 bg-background/80 backdrop-blur-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1.5",children:[t.jsx(ay.MD,{variant:"ghost",size:"icon",onClick:ez,disabled:k<=0||r,title:"Desfazer (Ctrl+Z)",children:t.jsx(u.Z,{className:"h-4 w-4"})}),t.jsx(ay.MD,{variant:"ghost",size:"icon",onClick:eP,disabled:k>=E.length-1||r,title:"Refazer (Ctrl+Y)",children:t.jsx(m.Z,{className:"h-4 w-4"})}),t.jsx("span",{className:"w-px h-4 bg-border mx-1"}),t.jsx(ay.MD,{variant:"ghost",size:"icon",onClick:eB,disabled:R||r,title:"Salvar (Ctrl+S)",children:R?t.jsx(p.Z,{className:"h-4 w-4 animate-spin"}):t.jsx(h.Z,{className:"h-4 w-4"})}),(0,t.jsxs)("div",{className:"hidden md:flex gap-1 ml-2",children:[t.jsx(e9,{workbookId:e,workbookName:j.name,sheets:[{name:j.name,data:j}]}),t.jsx(ab.UploadButton,{workbookId:e,saveToSupabase:!0,onUpload:e=>{e&&e.sheets&&e.sheets.length>0&&(ex(j),C(e.sheets[0].data))}})]})]}),(0,t.jsxs)("div",{className:"items-center gap-2 hidden md:flex",children:[t.jsx(an,{workbookId:e,className:"mr-2"}),(0,t.jsxs)(S.Button,{variant:"ghost",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>V(!B),children:[B?t.jsx(x.Z,{className:"h-3 w-3"}):t.jsx(f.Z,{className:"h-3 w-3"}),B?"Mostrar":"Ocultar"," AI"]}),(0,t.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>_(!0),children:[t.jsx(g.Z,{className:"h-3 w-3"}),"Comandos"]}),(0,t.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-8 gap-1 text-xs",onClick:()=>{},children:[t.jsx(y.Z,{className:"h-3 w-3"}),"Tela Cheia"]})]}),t.jsx("div",{className:"flex md:hidden",children:(0,t.jsxs)(S.Button,{variant:"outline",size:"sm",className:"h-8",onClick:()=>M(!0),children:[t.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"AI Chat"]})})]}),(0,t.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex-1 overflow-hidden relative",children:[ar,t.jsx("div",{ref:ae,className:"overflow-auto border border-border rounded-md",style:{height:"400px",width:"100%"},children:(0,t.jsxs)("div",{className:"table w-full relative",children:[t.jsx("div",{ref:aa,className:"table-header-group sticky top-0 bg-background z-10",children:(0,t.jsxs)("div",{className:"table-row",children:[t.jsx("div",{className:"table-cell w-10 text-center text-xs font-medium bg-muted",children:"#"}),j.headers.map((e,a)=>(0,t.jsxs)("div",{className:"table-cell p-2 font-medium bg-muted",children:[e,!r&&t.jsx(ay.Kk,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 ml-1",actionId:`column-${a}`,onAction:()=>eY(a),"aria-label":`Remover coluna ${e}`,children:t.jsx(d.Z,{className:"h-3 w-3"})})]},a)),t.jsx("div",{className:"table-cell w-10 bg-muted"})]})}),t.jsx("div",{className:"table-row-group relative",style:{height:`${as.getTotalSize()}px`},children:as.getVirtualItems().map(e=>{let a=j.rows[e.index]||[];return t.jsx("div",{className:"table-row",style:{position:"absolute",top:0,left:0,width:"100%",height:`${e.size}px`,transform:`translateY(${e.start}px)`},children:t.jsx(aO,{rowIndex:e.index,rowData:a,headers:j.headers,modifiedCellsMap:eU,readOnly:r,onCellChange:eq,onRemoveRow:eG})},e.index)})})]})}),ey&&ey.length>0&&t.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg animate-in fade-in slide-in-from-bottom-5 duration-300",children:1===ey.length?"C\xe9lula atualizada":`${ey.length} c\xe9lulas atualizadas`})]}),t.jsx("div",{className:`
            h-full border-l overflow-hidden transition-all duration-300 ease-in-out
            ${B?"w-0 opacity-0":"w-80 opacity-100"}
            hidden md:block
          `,"data-tutorial-target":"ai-assistant",children:(0,t.jsxs)("div",{className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"p-3 border-b flex items-center justify-between",children:[(0,t.jsxs)("h3",{className:"font-semibold text-sm flex items-center",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),t.jsx(S.Button,{variant:"ghost",size:"icon",onClick:()=>V(!0),children:t.jsx(f.Z,{className:"h-4 w-4"})})]}),t.jsx(P.x,{className:"flex-1 p-3",children:0===ek.length?(0,t.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[t.jsx(c.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),t.jsx("h3",{className:"font-medium mb-1",children:"Excel Assistente"}),t.jsx("p",{className:"text-sm max-w-xs mx-auto",children:"Utilize linguagem natural para manipular sua planilha. Digite comandos como:"}),t.jsx("div",{className:"mt-4 space-y-2 text-xs",children:e6})]}):t.jsx("div",{className:"space-y-4",children:ek.map((e,a)=>(0,t.jsxs)("div",{className:`
                        p-3 rounded-lg text-sm
                        ${"user"===e.role?"bg-primary/10 border border-primary/20":"bg-muted"}
                      `,children:[t.jsx("div",{className:"text-xs font-medium mb-1 text-muted-foreground",children:"user"===e.role?"Voc\xea":"Excel Copilot"}),t.jsx("div",{className:"whitespace-pre-wrap",children:e.content})]},a))})}),t.jsx("div",{className:"p-3 border-t",children:t.jsx(U,{onSendMessage:ej,isLoading:eg,placeholder:"Digite um comando...",onChange:e5,showExamples:!1})})]})})]}),B&&!D&&t.jsx(ay.MD,{variant:"default",size:"sm",className:"fixed right-4 bottom-4 shadow-lg rounded-full h-10 w-10 p-0",onClick:()=>V(!1),children:t.jsx(c.Z,{className:"h-4 w-4"})}),D&&(0,t.jsxs)("div",{className:`
      fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex flex-col
      ${L?"translate-y-0":"translate-y-full"}
      transition-transform duration-300 ease-in-out
    `,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,t.jsxs)("h3",{className:"font-semibold flex items-center",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Excel Copilot"]}),t.jsx(S.Button,{variant:"ghost",size:"icon",onClick:()=>M(!1),children:t.jsx(d.Z,{className:"h-5 w-5"})})]}),t.jsx(P.x,{className:"flex-1 p-4",children:0===ek.length?(0,t.jsxs)("div",{className:"text-center py-10 text-muted-foreground",children:[t.jsx(c.Z,{className:"h-8 w-8 mx-auto mb-3 text-primary/60"}),t.jsx("h3",{className:"text-lg font-medium mb-1",children:"Excel Copilot"}),t.jsx("p",{className:"text-sm max-w-sm mx-auto",children:"Envie comandos em linguagem natural para manipular sua planilha"})]}):t.jsx("div",{className:"space-y-4",children:ek.map((e,a)=>t.jsx("div",{className:`
                  p-3 rounded-lg max-w-[85%]
                  ${"user"===e.role?"bg-primary text-primary-foreground ml-auto":"bg-muted text-foreground border"}
                `,children:e.content},a))})}),t.jsx("div",{className:"p-4 border-t",children:t.jsx(U,{onSendMessage:ej,isLoading:eg,placeholder:"Digite um comando...",showExamples:0===ek.length})})]}),t.jsx(ac,{open:eD,onOpenChange:e$,children:(0,t.jsxs)(am,{className:"max-w-md",children:[(0,t.jsxs)(ap,{children:[(0,t.jsxs)(ax,{className:"flex items-center gap-2",children:[t.jsx(v.Z,{className:"h-5 w-5 text-amber-500"}),"Voc\xea est\xe1 chegando ao limite"]}),(0,t.jsxs)(af,{className:"text-base",children:["Voc\xea j\xe1 utilizou ",eT?.used||0," de ",eT?.limit||50," comandos dispon\xedveis no seu plano. Para continuar utilizando todos os recursos do Excel Copilot, escolha uma op\xe7\xe3o abaixo:"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4 my-4",children:[(0,t.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>e8(),children:[(0,t.jsxs)("h3",{className:"font-semibold flex items-center",children:[t.jsx(c.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Experimente o Plano Pro Gr\xe1tis por 7 dias"]}),t.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Acesso total a todos os recursos sem limita\xe7\xf5es durante 7 dias. Ser\xe1 necess\xe1rio informar um cart\xe3o, mas voc\xea pode cancelar a qualquer momento."})]}),(0,t.jsxs)("div",{className:"border rounded-lg p-4 hover:border-primary cursor-pointer",onClick:()=>e7(),children:[(0,t.jsxs)("h3",{className:"font-semibold flex items-center",children:[t.jsx(n.Z,{className:"h-4 w-4 mr-2 text-primary"}),"Fazer Upgrade para o Plano Pro"]}),t.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"R$20/m\xeas ou R$200/ano. Acesso ilimitado a todos os recursos premium."})]})]}),t.jsx(ah,{children:t.jsx(ag,{children:"Continuar no Plano Free"})})]})}),eT&&eT.used/eT.limit>=.8&&eT.used/eT.limit<1&&(0,t.jsxs)("div",{className:"bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50 border px-4 py-2 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(v.Z,{className:"h-4 w-4 text-amber-500"}),(0,t.jsxs)("span",{className:"text-sm text-amber-800 dark:text-amber-300",children:["Voc\xea utilizou ",Math.round(eT.used/eT.limit*100),"% do seu limite mensal de comandos."]})]}),t.jsx(S.Button,{variant:"ghost",size:"sm",onClick:()=>e$(!0),children:"Fazer Upgrade"})]}),eT&&eT.used>=eT.limit&&(0,t.jsxs)("div",{className:"bg-destructive/10 border-destructive/30 border px-4 py-2 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(v.Z,{className:"h-4 w-4 text-destructive"}),t.jsx("span",{className:"text-sm text-destructive",children:"Voc\xea atingiu 100% do seu limite mensal de comandos."})]}),t.jsx(S.Button,{variant:"outline",size:"sm",onClick:()=>e$(!0),children:"Fazer Upgrade Agora"})]}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-4 p-4 border-t dark:border-gray-800",children:[(0,t.jsxs)("div",{className:"flex-1",children:[es&&t.jsx(J,{command:et?.command||"",interpretation:es,isLoading:eg,onExecute:at,onCancel:ao}),el&&ei&&t.jsx(G,{commandId:ei.id,command:ei.command,onDismiss:ad,onFeedbackSubmit:ai}),t.jsx(U,{onSendMessage:ej,isLoading:eg,disabled:eg||r,autoFocus:!0,onChange:e5})]}),t.jsx("div",{className:"w-full lg:w-64 space-y-2"})]})]})}a_.displayName="MemoizedQuickCommandButton"},28612:(e,a,s)=>{"use strict";s.d(a,{M:()=>o});var r=s(51641);class t{constructor(){this.feedbackItems=[],this.feedbackStorage=null,this.STORAGE_KEY="excel_copilot_feedback",this.MAX_STORED_ITEMS=100}static getInstance(){return t.instance||(t.instance=new t),t.instance}async storeFeedback(e){let a={...e,timestamp:new Date().toISOString()};if(this.feedbackItems.unshift(a),this.feedbackItems.length>this.MAX_STORED_ITEMS&&(this.feedbackItems=this.feedbackItems.slice(0,this.MAX_STORED_ITEMS)),this.saveToStorage(),"true"===process.env.NEXT_PUBLIC_ENABLE_FEEDBACK_API)try{await fetch("/api/feedback",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}catch(e){r.logger.error("Erro ao enviar feedback para API:",e)}}getAnalytics(){let e=this.feedbackItems.length;if(0===e)return{totalCommands:0,successRate:0,commonIssues:[],commandPatterns:[]};let a=this.feedbackItems.filter(e=>e.successful).length,s=this.feedbackItems.filter(e=>!e.successful&&e.feedbackText),r=new Map;s.forEach(e=>{let a=e.feedbackText?.toLowerCase()||"",s=!1;for(let e of[{word:"entend",issue:"N\xe3o entendeu o comando"},{word:"error",issue:"Erro na execu\xe7\xe3o"},{word:"lent",issue:"Performance lenta"},{word:"format",issue:"Problemas de formata\xe7\xe3o"},{word:"gr\xe1fico",issue:"Problemas com gr\xe1ficos"},{word:"tabela",issue:"Problemas com tabelas"},{word:"f\xf3rmula",issue:"Problemas com f\xf3rmulas"}])a.includes(e.word)&&(r.set(e.issue,(r.get(e.issue)||0)+1),s=!0);!s&&a.length>0&&r.set("Outros problemas",(r.get("Outros problemas")||0)+1)});let t=Array.from(r.entries()).map(([e,a])=>({issue:e,count:a})).sort((e,a)=>a.count-e.count),o=new Map;return this.feedbackItems.forEach(e=>{let a=e.command.toLowerCase();for(let s of[{words:["cri","tabela"],pattern:"Criar tabela"},{words:["gr\xe1fico","chart"],pattern:"Criar gr\xe1fico"},{words:["calcul","m\xe9dia","soma"],pattern:"C\xe1lculos"},{words:["format","cor","estilo"],pattern:"Formata\xe7\xe3o"},{words:["filtr","ordem"],pattern:"Filtro/Ordena\xe7\xe3o"}])if(s.words.some(e=>a.includes(e))){let a=o.get(s.pattern)||{success:0,total:0};o.set(s.pattern,{success:a.success+(e.successful?1:0),total:a.total+1});break}}),{totalCommands:e,successRate:a/e*100,commonIssues:t,commandPatterns:Array.from(o.entries()).map(([e,a])=>({pattern:e,successRate:a.success/a.total*100,count:a.total})).sort((e,a)=>a.count-e.count)}}saveToStorage(){if(this.feedbackStorage)try{this.feedbackStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.feedbackItems))}catch(e){r.logger.error("Erro ao salvar feedback no localStorage:",e)}}loadFromStorage(){if(this.feedbackStorage)try{let e=this.feedbackStorage.getItem(this.STORAGE_KEY);e&&(this.feedbackItems=JSON.parse(e))}catch(e){r.logger.error("Erro ao carregar feedback do localStorage:",e),this.feedbackItems=[]}}}let o=t.getInstance()},96671:(e,a,s)=>{"use strict";var r;s.r(a),s.d(a,{DEFAULT_EXCEL_SYSTEM_PROMPT:()=>o,ExcelAIProcessor:()=>t,GeminiErrorType:()=>r,createExcelAIProcessor:()=>l,getGeminiServiceAPI:()=>n}),s(51641);class t{constructor(e={},a=!0){this.context={activeSheet:e.activeSheet||"Sheet1",headers:e.headers||[],selection:e.selection||"A1",recentOperations:e.recentOperations||[]},this.useRealAI=a}async processQuery(e){try{let a=this.preprocessQuery(e),s=this.buildPrompt(a),r=await n(),t=await r.sendMessage(s,{context:JSON.stringify(this.context),useMock:!this.useRealAI});return this.parseAIResponse(t)}catch(e){return console.error("Erro ao processar query:",e),{operations:[],error:`Erro ao processar: ${e instanceof Error?e.message:String(e)}`,success:!1,message:"Falha ao processar query com IA"}}}preprocessQuery(e){return e.replace(/\bform\./g,"f\xf3rmula").replace(/\bcol\./g,"coluna").replace(/\btab\./g,"tabela").replace(/\bgraf\./g,"gr\xe1fico").replace(/\bcel\./g,"c\xe9lula").replace(/\bfunc\./g,"fun\xe7\xe3o").replace(/\bop\./g,"opera\xe7\xe3o").replace(/\bval\./g,"valor").replace(/\bmed\./g,"m\xe9dia")}buildPrompt(e){return`
    Analise o seguinte comando para Excel e retorne as opera\xe7\xf5es necess\xe1rias em formato JSON:
    
    Comando: "${e}"
    
    Contexto da planilha:
    - Planilha ativa: ${this.context.activeSheet}
    - Sele\xe7\xe3o atual: ${this.context.selection}
    - Cabe\xe7alhos: ${this.context.headers?.join(", ")||"N/A"}
    
    Retorne APENAS um objeto JSON com a seguinte estrutura:
    {
      "operations": [
        {
          "type": "TIPO_OPERACAO", // FORMULA, CHART, TABLE, FORMAT, etc.
          "data": { ... }, // Dados espec\xedficos da opera\xe7\xe3o
          "description": "Descri\xe7\xe3o" // Opcional, descri\xe7\xe3o da opera\xe7\xe3o
        }
      ],
      "explanation": "Explica\xe7\xe3o do que foi feito" // Opcional
    }
    `}parseAIResponse(e){try{let a=e.match(/\{[\s\S]*\}/);if(a){let e=a[0],s=JSON.parse(e);if(!s.operations||!Array.isArray(s.operations))throw Error("Formato de resposta inv\xe1lido: operations n\xe3o \xe9 um array");return s}return{operations:[{type:"TABLE",data:{rawResponse:e},description:`Resposta em texto: ${e.substring(0,100)}...`}],explanation:"A resposta n\xe3o p\xf4de ser processada como JSON",success:!0,message:"Processamento parcial realizado"}}catch(a){return console.error("Erro ao analisar resposta da IA:",a),{operations:[{type:"TABLE",data:{error:!0},description:`Processando: "${e.substring(0,100)}..."`}],explanation:"Erro ao processar JSON da resposta",error:String(a),success:!1,message:"Falha ao analisar resposta da IA"}}}}let o=`
Voc\xea \xe9 um assistente especializado em Excel, capaz de ajudar a realizar opera\xe7\xf5es com planilhas.
Sua fun\xe7\xe3o \xe9 interpretar comandos em linguagem natural e convert\xea-los em opera\xe7\xf5es Excel espec\xedficas.

# DIRETRIZES IMPORTANTES
1. Sempre responda de forma estruturada usando o formato JSON abaixo
2. Para cada comando, identifique as opera\xe7\xf5es necess\xe1rias e forne\xe7a par\xe2metros precisos
3. Em caso de ambiguidade, escolha a interpreta\xe7\xe3o mais prov\xe1vel baseada no contexto
4. Se n\xe3o conseguir interpretar o comando, forne\xe7a uma resposta de erro amig\xe1vel
5. NUNCA invente dados ou colunas que n\xe3o existam no contexto atual

# FORMATO DE RESPOSTA
{
  "operations": [
    {
      "type": "TIPO_DA_OPERA\xc7\xc3O",
      "data": { ... par\xe2metros espec\xedficos da opera\xe7\xe3o ... }
    }
  ],
  "explanation": "Breve explica\xe7\xe3o do que ser\xe1 feito",
  "interpretation": "Como voc\xea entendeu o comando do usu\xe1rio"
}

# TIPOS DE OPERA\xc7\xd5ES DISPON\xcdVEIS

## F\xd3RMULAS
- FORMULA: Aplicar f\xf3rmulas em c\xe9lulas
  {
    "type": "FORMULA",
    "data": {
      "formula": "=SOMA(A1:A10)", 
      "range": "B1" | ["B1", "B2"] | "B1:B10"
    }
  }

## DADOS
- FILTER: Filtrar dados
  {
    "type": "FILTER",
    "data": {
      "column": "A" | 1,
      "condition": ">" | "<" | "=" | "contains" | "between",
      "value": 100 | "texto" | [10, 20]
    }
  }
- SORT: Ordenar dados
  {
    "type": "SORT",
    "data": {
      "column": "A" | 1,
      "direction": "asc" | "desc"
    }
  }

## VISUALIZA\xc7\xd5ES
- CHART: Criar ou modificar gr\xe1ficos
  {
    "type": "CHART",
    "data": {
      "type": "bar" | "line" | "pie" | "scatter" | "area",
      "title": "T\xedtulo do gr\xe1fico",
      "labels": "A1:A10", // Eixo X ou categorias
      "datasets": ["B1:B10", "C1:C10"], // S\xe9ries de dados
      "options": { ... op\xe7\xf5es adicionais ... }
    }
  }

## FORMATA\xc7\xc3O
- CONDITIONAL_FORMAT: Formata\xe7\xe3o condicional
  {
    "type": "CONDITIONAL_FORMAT",
    "data": {
      "range": "A1:B10",
      "rule": "greater" | "less" | "equal" | "between" | "text" | "date",
      "value": 100 | [10, 20] | "texto",
      "format": {
        "background": "#F5F5F5",
        "textColor": "#FF0000",
        "bold": true | false,
        "italic": true | false
      }
    }
  }

## TABELAS
- PIVOT_TABLE: Criar tabelas din\xe2micas
  {
    "type": "PIVOT_TABLE",
    "data": {
      "source": "A1:D10",
      "rows": ["A"], // Campos para linhas
      "columns": ["B"], // Campos para colunas
      "values": [{ "field": "C", "function": "sum" }], // Campos para valores
      "filters": [{ "field": "D", "value": "X" }] // Filtros opcionais
    }
  }

## C\xc9LULAS
- CELL_UPDATE: Atualizar c\xe9lulas individuais
  {
    "type": "CELL_UPDATE",
    "data": {
      "updates": [
        { "cell": "A1", "value": 100 },
        { "cell": "B1", "value": "Texto" }
      ]
    }
  }

## AN\xc1LISE
- DATA_ANALYSIS: An\xe1lise estat\xedstica
  {
    "type": "DATA_ANALYSIS",
    "data": {
      "type": "statistics" | "correlation" | "regression",
      "range": "A1:B10",
      "options": { ... op\xe7\xf5es espec\xedficas ... }
    }
  }

# EXEMPLOS DE COMANDOS E RESPOSTAS

## Exemplo 1: "Calcule a m\xe9dia da coluna B"
{
  "operations": [
    {
      "type": "FORMULA",
      "data": {
        "formula": "=M\xc9DIA(B:B)",
        "range": "C1"
      }
    }
  ],
  "explanation": "Calculando a m\xe9dia da coluna B e colocando o resultado na c\xe9lula C1",
  "interpretation": "Voc\xea deseja calcular a m\xe9dia de todos os valores num\xe9ricos na coluna B"
}

## Exemplo 2: "Crie uma tabela de vendas por m\xeas"
{
  "operations": [
    {
      "type": "CELL_UPDATE",
      "data": {
        "updates": [
          { "cell": "A1", "value": "M\xeas" },
          { "cell": "B1", "value": "Vendas" },
          { "cell": "A2", "value": "Janeiro" },
          { "cell": "A3", "value": "Fevereiro" },
          { "cell": "A4", "value": "Mar\xe7o" },
          { "cell": "B2", "value": 0 },
          { "cell": "B3", "value": 0 },
          { "cell": "B4", "value": 0 }
        ]
      }
    }
  ],
  "explanation": "Criando uma tabela de vendas por m\xeas com layout b\xe1sico",
  "interpretation": "Voc\xea deseja criar uma nova tabela para registrar vendas mensais"
}

## Exemplo 3: "Gere um gr\xe1fico de barras com os dados da coluna A e B"
{
  "operations": [
    {
      "type": "CHART",
      "data": {
        "type": "bar",
        "title": "Gr\xe1fico de Barras A vs B",
        "labels": "A1:A10",
        "datasets": ["B1:B10"],
        "options": {
          "legend": true,
          "horizontalBar": false
        }
      }
    }
  ],
  "explanation": "Criando um gr\xe1fico de barras usando dados das colunas A e B",
  "interpretation": "Voc\xea deseja visualizar os dados das colunas A e B em um gr\xe1fico de barras"
}

# CONTEXTO ATUAL DA PLANILHA
{contextInfo}
`;function l(e=!1){return new t({},e)}async function n(){return{async sendMessage(e,a={}){let s=await fetch("http://localhost:3000/api/ai/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:e,...a})});if(!s.ok)throw Error(`API Error: ${s.statusText}`);return(await s.json()).response}}}!function(e){e.NETWORK_ERROR="NETWORK_ERROR",e.API_ERROR="API_ERROR",e.RATE_LIMIT="RATE_LIMIT",e.INVALID_REQUEST="INVALID_REQUEST",e.AUTHENTICATION_ERROR="AUTHENTICATION_ERROR",e.UNKNOWN="UNKNOWN",e.API_UNAVAILABLE="API_UNAVAILABLE"}(r||(r={}))},96833:(e,a,s)=>{"use strict";s.d(a,{Nt:()=>l,Xf:()=>t}),s(89244);var r=s(89275);let t={FREE:"free",PRO_MONTHLY:"pro_monthly",PRO_ANNUAL:"pro_annual"};t.FREE,t.PRO_MONTHLY,t.PRO_ANNUAL;let o=process.env.STRIPE_SECRET_KEY||"";function l(e){switch(e){case t.FREE:return"Gr\xe1tis";case t.PRO_MONTHLY:return"Pro Mensal";case t.PRO_ANNUAL:return"Pro Anual";default:return"Desconhecido"}}process.env.STRIPE_WEBHOOK_SECRET,o&&new r.Z(o,{apiVersion:"2023-10-16",appInfo:{name:"Excel Copilot",version:"1.0.0"}})},64349:(e,a,s)=>{"use strict";s.d(a,{prisma:()=>n});var r=s(94007);let t={info:(e,...a)=>{},error:(e,...a)=>{console.error(`[DB ERROR] ${e}`,...a)},warn:(e,...a)=>{console.warn(`[DB WARNING] ${e}`,...a)}},o={totalQueries:0,failedQueries:0,averageQueryTime:0,connectionFailures:0,lastConnectionFailure:null},l=[],n=global.prisma||new r.PrismaClient({log:["error"],datasources:{db:{url:process.env.DB_DATABASE_URL||""}}});async function i(){try{await n.$disconnect(),t.info("Conex\xe3o com o banco de dados encerrada com sucesso")}catch(e){t.error("Erro ao desconectar do banco de dados",e)}}n.$on("query",e=>{o.totalQueries++,e.duration&&(l.push(e.duration),l.length>100&&l.shift(),o.averageQueryTime=l.reduce((e,a)=>e+a,0)/l.length),e.duration&&e.duration>500&&t.warn(`Consulta lenta detectada: ${Math.round(e.duration)}ms - Query: ${e.query||"Query desconhecida"}`)}),n.$on("error",e=>{o.failedQueries++,o.connectionFailures++,o.lastConnectionFailure=new Date().toISOString(),t.error(`Erro na conex\xe3o com o banco de dados: ${e.message||"Erro desconhecido"}`)}),"undefined"!=typeof process&&process.on("beforeExit",()=>{i()})}};