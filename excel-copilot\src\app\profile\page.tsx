'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { 
  User, 
  Mail, 
  Calendar, 
  Settings, 
  Camera, 
  Save,
  Loader2,
  Shield,
  BarChart3
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { AvatarUpload } from '@/components/profile/avatar-upload';

// Schema de validação para o formulário de perfil
const profileFormSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome muito longo'),
  email: z.string().email('Email inválido'),
  image: z.string().url('URL de imagem inválida').optional().or(z.literal('')),
});

type ProfileFormData = z.infer<typeof profileFormSchema>;

interface UserProfile {
  id: string;
  name: string | null;
  email: string | null;
  image: string | null;
  emailVerified: Date | null;
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt: Date | null;
  loginCount: number;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: 'pt-BR' | 'en-US';
    notifications: {
      email: boolean;
      push: boolean;
      marketing: boolean;
    };
    privacy: {
      profileVisible: boolean;
      shareUsageData: boolean;
    };
  };
  stats: {
    workbooksCount: number;
    memberSince: Date;
    lastLogin: Date | null;
    loginCount: number;
  };
  subscription: {
    plan: string;
    status: string;
    currentPeriodEnd: Date;
  } | null;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isDirty },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileFormSchema),
  });

  // Redirect se não estiver autenticado
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // Carregar dados do perfil
  useEffect(() => {
    const fetchProfile = async () => {
      if (!session?.user) return;

      try {
        const response = await fetch('/api/user/profile');
        const data = await response.json();

        if (response.ok) {
          setProfile(data.profile);
          // Preencher o formulário com os dados atuais
          setValue('name', data.profile.name || '');
          setValue('email', data.profile.email || '');
          setValue('image', data.profile.image || '');
        } else {
          toast.error('Erro ao carregar perfil: ' + data.error);
        }
      } catch (error) {
        toast.error('Erro ao carregar perfil');
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      fetchProfile();
    }
  }, [session, setValue]);

  // Função para salvar alterações
  const onSubmit = async (data: ProfileFormData) => {
    setSaving(true);
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Perfil atualizado com sucesso!');
        // Atualizar os dados locais
        if (profile) {
          setProfile({
            ...profile,
            ...result.profile,
          });
        }
      } else {
        toast.error('Erro ao atualizar perfil: ' + result.error);
      }
    } catch (error) {
      toast.error('Erro ao atualizar perfil');
    } finally {
      setSaving(false);
    }
  };

  // Função para atualizar avatar
  const handleAvatarUpdate = async (imageUrl: string) => {
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image: imageUrl }),
      });

      const result = await response.json();

      if (response.ok && profile) {
        setProfile({
          ...profile,
          image: imageUrl,
        });
        setValue('image', imageUrl);
      } else {
        toast.error('Erro ao atualizar avatar: ' + result.error);
      }
    } catch (error) {
      toast.error('Erro ao atualizar avatar');
    }
  };

  const getInitials = (name?: string | null) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const formatDate = (date: Date | string | null) => {
    if (!date) return 'Nunca';
    return new Date(date).toLocaleDateString('pt-BR');
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-4xl">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!session || !profile) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <div className="flex items-center space-x-4 mb-8">
        <AvatarUpload
          currentImage={profile.image}
          userName={profile.name}
          onImageUpdate={handleAvatarUpdate}
        />
        <div>
          <h1 className="text-3xl font-bold">{profile.name || 'Usuário'}</h1>
          <p className="text-muted-foreground">{profile.email}</p>
          <div className="flex items-center space-x-2 mt-2">
            {profile.subscription && (
              <Badge variant="secondary">
                {profile.subscription.plan.toUpperCase()}
              </Badge>
            )}
            {profile.emailVerified && (
              <Badge variant="outline" className="text-green-600">
                <Shield className="h-3 w-3 mr-1" />
                Verificado
              </Badge>
            )}
          </div>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">Perfil</TabsTrigger>
          <TabsTrigger value="stats">Estatísticas</TabsTrigger>
          <TabsTrigger value="preferences">Preferências</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Informações do Perfil</CardTitle>
              <CardDescription>
                Atualize suas informações pessoais
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome</Label>
                  <Input
                    id="name"
                    {...register('name')}
                    placeholder="Seu nome completo"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('email')}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-500">{errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="image">URL da Imagem</Label>
                  <Input
                    id="image"
                    {...register('image')}
                    placeholder="https://exemplo.com/sua-foto.jpg"
                  />
                  {errors.image && (
                    <p className="text-sm text-red-500">{errors.image.message}</p>
                  )}
                </div>

                <Button 
                  type="submit" 
                  disabled={!isDirty || saving}
                  className="w-full sm:w-auto"
                >
                  {saving ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Salvar Alterações
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Planilhas Criadas</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profile.stats.workbooksCount}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total de Logins</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{profile.stats.loginCount}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Membro Desde</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatDate(profile.stats.memberSince)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Último Login</CardTitle>
                <Mail className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatDate(profile.stats.lastLogin)}</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="preferences">
          <Card>
            <CardHeader>
              <CardTitle>Preferências</CardTitle>
              <CardDescription>
                Configure suas preferências de uso
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Tema</h4>
                  <p className="text-sm text-muted-foreground">
                    Atual: {profile.preferences.theme}
                  </p>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="text-sm font-medium mb-2">Idioma</h4>
                  <p className="text-sm text-muted-foreground">
                    Atual: {profile.preferences.language}
                  </p>
                </div>

                <Separator />

                <div>
                  <h4 className="text-sm font-medium mb-2">Notificações</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>Email: {profile.preferences.notifications.email ? 'Ativado' : 'Desativado'}</p>
                    <p>Push: {profile.preferences.notifications.push ? 'Ativado' : 'Desativado'}</p>
                    <p>Marketing: {profile.preferences.notifications.marketing ? 'Ativado' : 'Desativado'}</p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="text-sm font-medium mb-2">Privacidade</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>Perfil Visível: {profile.preferences.privacy.profileVisible ? 'Sim' : 'Não'}</p>
                    <p>Compartilhar Dados de Uso: {profile.preferences.privacy.shareUsageData ? 'Sim' : 'Não'}</p>
                  </div>
                </div>

                <Button variant="outline" className="w-full sm:w-auto">
                  <Settings className="h-4 w-4 mr-2" />
                  Configurar Preferências
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
