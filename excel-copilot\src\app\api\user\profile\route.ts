// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { z } from 'zod';

import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';
import { SessionUser } from '@/types/next-auth';
import { authOptions } from '@/server/auth/options';

// Schema de validação para atualização de perfil
const updateProfileSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório').max(100, 'Nome muito longo').optional(),
  email: z.string().email('Email inválido').optional(),
  image: z.string().url('URL de imagem inválida').optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    language: z.enum(['pt-BR', 'en-US']).optional(),
    notifications: z.object({
      email: z.boolean().optional(),
      push: z.boolean().optional(),
      marketing: z.boolean().optional(),
    }).optional(),
    privacy: z.object({
      profileVisible: z.boolean().optional(),
      shareUsageData: z.boolean().optional(),
    }).optional(),
  }).optional(),
});

type UpdateProfileData = z.infer<typeof updateProfileSchema>;

/**
 * GET /api/user/profile
 * Retorna os dados completos do perfil do usuário
 */
export async function GET(_req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = (session.user as SessionUser).id;

    // Buscar dados completos do usuário
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        loginCount: true,
        // Não incluir campos sensíveis como isBanned, etc.
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Usuário não encontrado.' },
        { status: 404 }
      );
    }

    // Buscar preferências (simuladas por enquanto - podem ser adicionadas ao schema depois)
    const preferences = {
      theme: 'system' as const,
      language: 'pt-BR' as const,
      notifications: {
        email: true,
        push: true,
        marketing: false,
      },
      privacy: {
        profileVisible: true,
        shareUsageData: false,
      },
    };

    // Buscar estatísticas do usuário
    const [workbooksCount, subscriptions] = await Promise.all([
      prisma.workbook.count({
        where: { userId },
      }),
      prisma.subscription.findFirst({
        where: {
          userId,
          OR: [{ status: 'active' }, { status: 'trialing' }],
        },
        orderBy: { createdAt: 'desc' },
      }),
    ]);

    const profile = {
      ...user,
      preferences,
      stats: {
        workbooksCount,
        memberSince: user.createdAt,
        lastLogin: user.lastLoginAt,
        loginCount: user.loginCount,
      },
      subscription: subscriptions ? {
        plan: subscriptions.plan,
        status: subscriptions.status,
        currentPeriodEnd: subscriptions.currentPeriodEnd,
      } : null,
    };

    logger.info('Perfil do usuário consultado', { userId });

    return NextResponse.json({ profile });
  } catch (error) {
    logger.error('Erro ao buscar perfil do usuário', { error });
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/user/profile
 * Atualiza os dados do perfil do usuário
 */
export async function PATCH(req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = (session.user as SessionUser).id;

    // Parse e validação do body
    const body = await req.json();
    const validationResult = updateProfileSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Dados inválidos',
          details: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const updateData: UpdateProfileData = validationResult.data;

    // Preparar dados para atualização no banco
    const prismaUpdateData: any = {};
    
    if (updateData.name !== undefined) {
      prismaUpdateData.name = updateData.name;
    }
    
    if (updateData.email !== undefined) {
      // Verificar se o email já está em uso por outro usuário
      const existingUser = await prisma.user.findFirst({
        where: {
          email: updateData.email,
          NOT: { id: userId },
        },
      });

      if (existingUser) {
        return NextResponse.json(
          { error: 'Este email já está em uso por outro usuário.' },
          { status: 409 }
        );
      }

      prismaUpdateData.email = updateData.email;
      // Reset emailVerified se o email mudou
      prismaUpdateData.emailVerified = null;
    }

    if (updateData.image !== undefined) {
      prismaUpdateData.image = updateData.image;
    }

    // Atualizar no banco de dados
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...prismaUpdateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        image: true,
        emailVerified: true,
        updatedAt: true,
      },
    });

    logger.info('Perfil do usuário atualizado', { 
      userId, 
      updatedFields: Object.keys(prismaUpdateData) 
    });

    return NextResponse.json({ 
      message: 'Perfil atualizado com sucesso',
      profile: updatedUser 
    });

  } catch (error) {
    logger.error('Erro ao atualizar perfil do usuário', { error });
    return NextResponse.json(
      { error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
