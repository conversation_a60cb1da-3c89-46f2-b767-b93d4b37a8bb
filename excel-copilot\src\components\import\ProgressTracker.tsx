'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, Clock, FileSpreadsheet, Database, Zap } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

export interface ProgressStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  startTime?: Date | undefined;
  endTime?: Date | undefined;
  error?: string | undefined;
  details?: string | undefined;
}

interface ProgressTrackerProps {
  steps: ProgressStep[];
  currentStep?: string | undefined;
  overallProgress: number;
  isComplete: boolean;
  hasError: boolean;
  onCancel?: (() => void) | undefined;
  showDetails?: boolean | undefined;
}

const STEP_ICONS: Record<string, React.ReactNode> = {
  'file-analysis': <FileSpreadsheet className="h-4 w-4" />,
  'schema-validation': <CheckCircle className="h-4 w-4" />,
  'data-transformation': <Zap className="h-4 w-4" />,
  'data-processing': <Database className="h-4 w-4" />,
  'finalization': <CheckCircle className="h-4 w-4" />,
};

export function ProgressTracker({
  steps,
  currentStep,
  overallProgress,
  isComplete,
  hasError,
  onCancel,
  showDetails = true,
}: ProgressTrackerProps) {
  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(new Date());

  useEffect(() => {
    if (!isComplete && !hasError) {
      const interval = setInterval(() => {
        setElapsedTime(Date.now() - startTime.getTime());
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isComplete, hasError, startTime]);

  const formatTime = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  const getStepDuration = (step: ProgressStep) => {
    if (step.startTime && step.endTime) {
      return formatTime(step.endTime.getTime() - step.startTime.getTime());
    }
    if (step.startTime && step.status === 'running') {
      return formatTime(Date.now() - step.startTime.getTime());
    }
    return null;
  };

  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const errorSteps = steps.filter(step => step.status === 'error').length;

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Progresso da Importação
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant={hasError ? "destructive" : isComplete ? "default" : "secondary"}>
              {hasError ? "Erro" : isComplete ? "Concluído" : "Processando"}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {formatTime(elapsedTime)}
            </span>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Progresso Geral */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progresso Geral</span>
            <span>{Math.round(overallProgress)}%</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{completedSteps}/{steps.length} etapas concluídas</span>
            {errorSteps > 0 && (
              <span className="text-destructive">{errorSteps} erro(s)</span>
            )}
          </div>
        </div>

        {/* Lista de Etapas */}
        <div className="space-y-3">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-start gap-3 p-3 rounded-lg border transition-colors ${
                step.status === 'running' ? 'bg-blue-50 border-blue-200' :
                step.status === 'completed' ? 'bg-green-50 border-green-200' :
                step.status === 'error' ? 'bg-red-50 border-red-200' :
                'bg-muted/20'
              }`}
            >
              {/* Ícone do Status */}
              <div className={`flex-shrink-0 mt-0.5 ${
                step.status === 'running' ? 'text-blue-600' :
                step.status === 'completed' ? 'text-green-600' :
                step.status === 'error' ? 'text-red-600' :
                'text-muted-foreground'
              }`}>
                {step.status === 'running' ? (
                  <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : step.status === 'completed' ? (
                  <CheckCircle className="h-4 w-4" />
                ) : step.status === 'error' ? (
                  <AlertCircle className="h-4 w-4" />
                ) : (
                  <Clock className="h-4 w-4" />
                )}
              </div>

              {/* Conteúdo da Etapa */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {STEP_ICONS[step.id]}
                    <h4 className="font-medium text-sm">{step.name}</h4>
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    {getStepDuration(step) && (
                      <span>{getStepDuration(step)}</span>
                    )}
                    {step.status === 'running' && (
                      <span>{Math.round(step.progress)}%</span>
                    )}
                  </div>
                </div>

                <p className="text-xs text-muted-foreground mt-1">
                  {step.description}
                </p>

                {/* Progresso da Etapa */}
                {step.status === 'running' && step.progress > 0 && (
                  <Progress value={step.progress} className="h-1 mt-2" />
                )}

                {/* Detalhes da Etapa */}
                {showDetails && step.details && (
                  <p className="text-xs text-muted-foreground mt-1 font-mono">
                    {step.details}
                  </p>
                )}

                {/* Erro da Etapa */}
                {step.status === 'error' && step.error && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-xs">
                      {step.error}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Estatísticas Finais */}
        {(isComplete || hasError) && (
          <div className="pt-4 border-t">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-green-600">{completedSteps}</p>
                <p className="text-xs text-muted-foreground">Concluídas</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-red-600">{errorSteps}</p>
                <p className="text-xs text-muted-foreground">Erros</p>
              </div>
              <div>
                <p className="text-2xl font-bold">{formatTime(elapsedTime)}</p>
                <p className="text-xs text-muted-foreground">Tempo Total</p>
              </div>
            </div>
          </div>
        )}

        {/* Ações */}
        {!isComplete && !hasError && onCancel && (
          <div className="flex justify-end pt-4 border-t">
            <button
              onClick={onCancel}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              Cancelar Importação
            </button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Hook para gerenciar o progresso
export function useProgressTracker() {
  const [steps, setSteps] = useState<ProgressStep[]>([]);
  const [currentStep, setCurrentStep] = useState<string | undefined>();
  const [overallProgress, setOverallProgress] = useState(0);

  const initializeSteps = (stepDefinitions: Omit<ProgressStep, 'status' | 'progress'>[]) => {
    const initialSteps: ProgressStep[] = stepDefinitions.map(step => ({
      ...step,
      status: 'pending',
      progress: 0,
    }));
    setSteps(initialSteps);
    setOverallProgress(0);
  };

  const startStep = (stepId: string, details?: string | undefined) => {
    setCurrentStep(stepId);
    setSteps(prev => prev.map(step =>
      step.id === stepId
        ? { ...step, status: 'running' as const, startTime: new Date(), details: details || undefined }
        : step
    ));
  };

  const updateStepProgress = (stepId: string, progress: number, details?: string | undefined) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId
        ? { ...step, progress, details: details || undefined }
        : step
    ));

    // Calcular progresso geral
    const totalSteps = steps.length;
    const completedSteps = steps.filter(s => s.status === 'completed').length;
    const currentStepProgress = progress / 100;
    setOverallProgress(((completedSteps + currentStepProgress) / totalSteps) * 100);
  };

  const completeStep = (stepId: string, details?: string | undefined) => {
    setSteps(prev => prev.map(step =>
      step.id === stepId
        ? { ...step, status: 'completed' as const, progress: 100, endTime: new Date(), details: details || undefined }
        : step
    ));
  };

  const errorStep = (stepId: string, error: string) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status: 'error', endTime: new Date(), error }
        : step
    ));
  };

  const isComplete = steps.length > 0 && steps.every(step => step.status === 'completed');
  const hasError = steps.some(step => step.status === 'error');

  return {
    steps,
    currentStep,
    overallProgress,
    isComplete,
    hasError,
    initializeSteps,
    startStep,
    updateStepProgress,
    completeStep,
    errorStep,
  };
}
